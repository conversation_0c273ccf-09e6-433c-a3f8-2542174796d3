import {
  FirebaseSubscriptionDocument,
  NOTIFICATION_TYPE,
  PushNotificationSubscriptionDocument,
} from '../modules/user/notifications/notification.schema';
import webPush from 'web-push';
import { Model } from 'mongoose';
import { FirebaseRepository } from '../repositories/firebase.repository';
import { NotificationService } from '../modules/user/notifications/notification.service';

export const sendNotification = async (
  webPushDestinations: PushNotificationSubscriptionDocument[],
  firebaseDestination: FirebaseSubscriptionDocument[],
  message: { title: string; message: string; path: string; type: NOTIFICATION_TYPE; data?: Record<string, any> },
  notificationService?: NotificationService,
  userId?: string,
  storeId?: string,
) => {
  if (notificationService && userId) {
    // If we have a notification service and user ID, use the service to send notifications
    await notificationService.sendNotification(userId, {
      title: message.title,
      message: message.message,
      path: message.path,
      type: message.type,
      data: message.data,
      store: storeId,
    });

    return [{ status: 'fulfilled' }];
  } else {
    // Legacy implementation for backward compatibility
    // This part can be gradually removed as all code migrates to using the notification service
    console.warn('Using deprecated notification sending method. Please update to use NotificationService.');

    const webPushPromises = webPushDestinations.map(async (subscription) => {
      try {
        // Actually send the web push notification
        await webPush.sendNotification(
          {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.public_key,
              auth: subscription.private_key,
            },
          },
          JSON.stringify({
            title: message.title,
            body: message.message,
            data: { path: message.path, ...message.data },
          }),
        );
        return { status: 'fulfilled' };
      } catch (e: any) {
        console.debug('Failed sending the web-push notification [' + e + ']');
        return { status: 'rejected', error: e.message };
      }
    });

    let firebaseResults: { status: string; error?: string }[] = [];
    if (firebaseDestination.length > 0) {
      // Note: Firebase notifications should be handled by FirebaseRepository
      // This is a simplified implementation for the legacy fallback
      firebaseResults = firebaseDestination.map((subscription) => {
        try {
          // In a real implementation, you'd use FirebaseRepository here
          // For now, we'll just log that we would send a Firebase notification
          console.log(`Would send Firebase notification to token: ${subscription.fcm_token?.substring(0, 20)}...`);
          return { status: 'fulfilled' };
        } catch (error) {
          return { status: 'rejected', error: error.message };
        }
      });
    }

    const webPushResults = await Promise.all(webPushPromises);
    const allResults = [...webPushResults, ...firebaseResults];
    return allResults;
  }
};
