import { Injectable, Logger } from '@nestjs/common';
import { ZillaConfig } from '../config/types/zilla.config';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { registerErrors } from '../utils/errors.util';
import { Errorable } from '../utils/types';
import { SlackConfig } from '../config/types/slack.config';
import { toCurrency } from '../utils';
import { toNaira } from '../utils/functions';
import { COUNTRY_FLAG_EMOJIS, CURRENCY_FLAG_EMOJIS, CURRENCIES } from '../modules/country/country.schema';

const channelUrls = {
  SIGNUPS: 'B05JAFFC24T/b8txBzNGiwrRYwCritSbRUlU',
  SUBSCRIPTIONS: 'B07FF1K757D/YVvafabKGeIxV8PRjvtSjMaC',
  KYC_AND_WALLET: 'B08N73N6JV6/xTJsafouttK4JMnNDlOJFAkd',
  MANUAL_WITHDRAWAL: 'B08MUDNL1MY/dw5JRgpvRXo8A5I4x8YEWiUc',
  DOMAIN_PURCHASE: 'B08UB2H8BAB/blWGMbVHEoVVgYWUaoLd4uA8',
  GENERAL: 'B08MN70L8SU/wQUXmfscfBJPsBiqweNxyQY2',
};

@Injectable()
export class SlackRepository {
  private config: SlackConfig;
  private readonly axios: AxiosInstance;

  constructor(configService: ConfigService, private readonly logger: Logger) {
    this.config = configService.get<SlackConfig>('slackConfiguration');
    this.axios = Axios.create({
      baseURL: this.config?.api_url,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async sendSignUpNotification(payload: {
    name: string;
    country: string;
    store_name: string;
    phone: string;
    whatsapp: string;
    store_slug: string;
    business_type: string;
    monthly_orders: string;
    business_category: string;
    social_media_platform: string;
    social_media_username: string;
    userId?: string;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.axios.post(`/${channelUrls.SIGNUPS}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'New Signup 🎉',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Country󠁥󠁳󠁰󠁶󠁿*\n${payload.country}`,
              },
            ],
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Phone*\n<tel:${payload.phone}>`,
              },
              {
                type: 'mrkdwn',
                text: `*Store Name*\n${payload.store_name}`,
              },
            ],
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Business Type*\n${payload.business_type}`,
              },
              {
                type: 'mrkdwn',
                text: `*Monthly Orders*\n${payload.monthly_orders}`,
              },
            ],
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Business Category*\n${payload.business_category}`,
              },
              {
                type: 'mrkdwn',
                text: `*Social Media*\n <https://${payload.social_media_platform}.com/${payload.social_media_username}> (${payload.social_media_username})`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact User',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'click_me_123',
              url: encodeURI(
                `https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi, ${
                  payload.name.split(' ')[0]
                } 👋🏾\n\nI'm [YOUR NAME] from Catlog.\n\nI hope you're having an amazing day? I noticed you recently signed up to Catlog, Did you find the process easy? Were you able to fully setup?\n\nHere's the store you created: https://${
                  payload.store_slug
                }.catlog.shop`,
              ),
              action_id: 'button-action',
            },
          },
          ...(payload.userId
            ? [
                {
                  type: 'divider',
                },
                {
                  type: 'section',
                  text: {
                    type: 'mrkdwn',
                    text: 'Take Action',
                  },
                  accessory: {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: 'View & Mark as Qualified',
                      emoji: true,
                    },
                    value: 'view_user_profile',
                    url: `https://app.catlog.shop/new-internals/users/${payload.userId}`,
                    action_id: 'view-user-profile-action',
                  },
                },
              ]
            : []),
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendSubscriptionNotification(payload: {
    name: string;
    amount: string;
    country: string;
    store_name: string;
    whatsapp: string;
    store_slug: string;
    plan_name: string;
    isNewSubscriber: boolean;
    first_subscription_date: string;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.axios.post(`/${channelUrls.SUBSCRIPTIONS}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'New Subscription Payment 💸',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Country󠁥󠁳󠁰󠁶󠁿*\n${payload.country}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Plan Name*\n${payload.plan_name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Amount Paid*\n${payload.amount}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Status*\n${payload.isNewSubscriber ? 'New Subscriber' : 'Returning Subscriber'}`,
              },
              {
                type: 'mrkdwn',
                text: `*First Subscription Date*\n${payload.first_subscription_date}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact User',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'click_me_123',
              url: encodeURI(`https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi`),
              action_id: 'button-action',
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendDropOffNotification(payload: {
    name: string;
    store_name: string;
    store_slug: string;
    country: string;
    whatsapp: string;
    plan_name: string;
    previousSubscriptionPayments: number;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.axios.post(`/${channelUrls.SUBSCRIPTIONS}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'Subscription DropOff 🔴',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Country󠁥󠁳󠁰󠁶󠁿*\n${payload.country}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Plan Name*\n${payload.plan_name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Prev Payments*\n${payload.previousSubscriptionPayments}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact User',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'click_me_123',
              url: encodeURI(`https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi`),
              action_id: 'button-action',
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendManualKycVerificationNotification(payload: {
    name: string;
    country: string;
    store_name: string;
    store_slug: string;
    whatsapp: string;
    id_type: string;
    id_number: string;
    social_links?: string;
    cta_url?: string;
    cta_label?: string;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.axios.post(`/${channelUrls.KYC_AND_WALLET}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'Manual KYC Request 🪪',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Country*\n${payload.country}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Store Name*\n${payload.store_name}`,
              },
              {
                type: 'mrkdwn',
                text: `*ID Type*\n${payload.id_type}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*ID Number*\n${payload.id_number}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact User',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'click_me_123',
              url: encodeURI(`https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi`),
              action_id: 'button-action',
            },
          },
          ...(payload.social_links
            ? [
                {
                  type: 'section',
                  text: {
                    type: 'mrkdwn',
                    text: `*Social Links*\n${payload.social_links}`,
                  },
                },
              ]
            : []),
          ...(payload.cta_url && payload.cta_label
            ? [
                {
                  type: 'divider',
                },
                {
                  type: 'section',
                  text: {
                    type: 'mrkdwn',
                    text: 'Take Action',
                  },
                  accessory: {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: payload.cta_label,
                      emoji: true,
                    },
                    value: 'approve-reject-action',
                    url: payload.cta_url,
                    action_id: 'approve-reject-action',
                  },
                },
              ]
            : []),
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendWalletRequestNotification(payload: {
    name: string;
    country: string;
    store_name: string;
    store_slug: string;
    whatsapp: string;
    requested_currencies: string[];
    reason: string;
    collect_payments_from_abroad: boolean;
    current_payment_method?: string;
    plans_to_get_customers_abroad?: string;
    cta_url?: string;
    cta_label?: string;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.axios.post(`/${channelUrls.KYC_AND_WALLET}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'New Wallet Request 💰',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Country*\n${payload.country}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Store Name*\n${payload.store_name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Requested Currencies*\n${payload.requested_currencies.join(', ')}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Reason*\n${payload.reason}`,
              },
              {
                type: 'mrkdwn',
                text: `*Collect Payments from Abroad*\n${payload.collect_payments_from_abroad ? 'Yes' : 'No'}`,
              },
            ],
          },
          ...(payload.current_payment_method
            ? [
                {
                  type: 'divider',
                },
                {
                  type: 'section',
                  fields: [
                    {
                      type: 'mrkdwn',
                      text: `*Current Payment Method*\n${payload.current_payment_method}`,
                    },
                  ],
                },
              ]
            : []),
          ...(payload.plans_to_get_customers_abroad
            ? [
                {
                  type: 'divider',
                },
                {
                  type: 'section',
                  fields: [
                    {
                      type: 'mrkdwn',
                      text: `*Plans to Get Customers Abroad*\n${payload.plans_to_get_customers_abroad}`,
                    },
                  ],
                },
              ]
            : []),
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact User',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'click_me_123',
              url: encodeURI(`https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi`),
              action_id: 'button-action',
            },
          },
          ...(payload.cta_url && payload.cta_label
            ? [
                {
                  type: 'divider',
                },
                {
                  type: 'section',
                  text: {
                    type: 'mrkdwn',
                    text: 'Take Action',
                  },
                  accessory: {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: payload.cta_label,
                      emoji: true,
                    },
                    value: 'approve_reject_wallet_request',
                    url: payload.cta_url,
                    action_id: 'approve-reject-wallet-action',
                  },
                },
              ]
            : []),
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendManualWithdrawalNotification(payload: {
    name: string;
    country: string;
    store_name: string;
    store_slug: string;
    whatsapp: string;
    amount: string;
    currency: string;
    account_name: string;
    account_number: string;
    bank_name: string;
    cta_url?: string;
    cta_label?: string;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    // Get country code from the country name (assuming it's a 2-letter code)
    const countryCode = payload.country.substring(0, 2).toUpperCase();
    const countryFlag = COUNTRY_FLAG_EMOJIS[countryCode] || '';

    // Get currency flag
    const currencyFlag = CURRENCY_FLAG_EMOJIS[payload.currency] || '';

    try {
      const res = await this.axios.post(`/${channelUrls.MANUAL_WITHDRAWAL}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'Manual Withdrawal Request 💰',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Country*\n${countryFlag} ${payload.country}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Store Name*\n${payload.store_name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Amount*\n${payload.amount} ${currencyFlag}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Account Name*\n${payload.account_name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Account Number*\n${payload.account_number}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Bank Name*\n${payload.bank_name}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact User',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'click_me_123',
              url: encodeURI(`https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi`),
              action_id: 'button-action',
            },
          },
          ...(payload.cta_url && payload.cta_label
            ? [
                {
                  type: 'divider',
                },
                {
                  type: 'section',
                  text: {
                    type: 'mrkdwn',
                    text: 'Take Action',
                  },
                  accessory: {
                    type: 'button',
                    text: {
                      type: 'plain_text',
                      text: payload.cta_label,
                      emoji: true,
                    },
                    value: 'process_manual_withdrawal',
                    url: payload.cta_url,
                    action_id: 'process-manual-withdrawal-action',
                  },
                },
              ]
            : []),
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendSubscriptionPaymentUpdate(blocks: any[]) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    try {
      const res = await this.axios.post(`/${channelUrls.GENERAL}`, {
        blocks,
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }

  async sendDomainDnsSetupNotification(payload: {
    name: string;
    email: string;
    country: string;
    store_name: string;
    store_slug: string;
    whatsapp: string;
    domain: string;
    amount: string;
    currency: string;
    provider: string;
    payment_reference: string;
    registration_id?: string;
    expires_at?: string;
  }) {
    if (this.config.isTesting) return new Promise((res) => res(undefined));

    // Get country code from the country name (assuming it's a 2-letter code)
    const countryCode = payload.country.substring(0, 2).toUpperCase();
    const countryFlag = COUNTRY_FLAG_EMOJIS[countryCode] || '';

    // Get currency flag
    const currencyFlag = CURRENCY_FLAG_EMOJIS[payload.currency] || '';

    try {
      const res = await this.axios.post(`/${channelUrls.DOMAIN_PURCHASE}`, {
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: 'Domain DNS Setup Required 🌐',
              emoji: true,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Customer Name*\n${payload.name}`,
              },
              {
                type: 'mrkdwn',
                text: `*Email*\n${payload.email}`,
              },
            ],
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Country*\n${countryFlag} ${payload.country}`,
              },
              {
                type: 'mrkdwn',
                text: `*Store Name*\n${payload.store_name}`,
              },
            ],
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Domain*\n${payload.domain}`,
              },
            ],
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Amount Paid*\n${payload.amount} ${currencyFlag}`,
              },
              {
                type: 'mrkdwn',
                text: `*Provider*\n${payload.provider}`,
              },
            ],
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Payment Reference*\n${payload.payment_reference}`,
              },
              {
                type: 'mrkdwn',
                text: `*Registration ID*\n${payload.registration_id || 'N/A'}`,
              },
            ],
          },
          ...(payload.expires_at
            ? [
                {
                  type: 'section',
                  fields: [
                    {
                      type: 'mrkdwn',
                      text: `*Expires At*\n${new Date(payload.expires_at).toLocaleDateString()}`,
                    },
                  ],
                },
              ]
            : []),
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Required Action:*\nManually configure DNS records for ${payload.domain} to point to the Catlog servers.\n\n*Steps:*\n1. Login to ${payload.provider} control panel\n2. Add A record: @ → Server IP`,
            },
          },
          {
            type: 'divider',
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `Go to store <${process.env.CATLOG_WWW}/${payload.store_slug}> 🔗`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'Contact Customer',
            },
            accessory: {
              type: 'button',
              text: {
                type: 'plain_text',
                text: 'Chat on Whatsapp 💬',
                emoji: true,
              },
              value: 'contact_domain_customer',
              url: encodeURI(
                `https://api.whatsapp.com/send/?phone=${payload.whatsapp}&text=Hi ${
                  payload.name.split(' ')[0]
                } 👋🏾\n\nCongratulations on your domain purchase! Your domain ${
                  payload.domain
                } has been successfully registered.\n\nWe're currently setting up the DNS configuration and your domain will be live within 24-48 hours.\n\nWe'll notify you once everything is ready! 🎉`,
              ),
              action_id: 'contact-domain-customer-action',
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: '\n',
              },
            ],
          },
        ],
      });
    } catch (err) {
      this.logger.error(err);
      registerErrors(err.response);

      return { error: err };
    }
  }
}
