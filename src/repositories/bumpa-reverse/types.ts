export interface BumpaStoreAddress {
  zip: string | null;
  city: string | null;
  phone: string | null;
  state: string | null;
  street: string | null;
  country: string | null;
}

export interface BumpaStoreSocial {
  twitter: string;
  facebook: string;
  linkedin: string;
  whatsapp: string;
  instagram: string;
  pinterest: string;
  snapchat: string;
  tiktok: string;
}

export interface BumpaPaymentSettings {
  paystack: boolean;
  terminal: boolean;
  bank_transfer: boolean;
}

export interface BumpaStoreSettings {
  social: BumpaStoreSocial;
  currency: string;
  use_themes: boolean;
  charge_customer: boolean;
  currency_symbol: string;
  payment_settings: BumpaPaymentSettings;
  maintenance_message: string;
  previous_payment_settings: BumpaPaymentSettings;
}

export interface BumpaOnBoardMeta {
  completed: boolean;
  hasShipping: boolean;
  created_first_product: boolean;
}

export interface BumpaReceiptMeta {
  plan_type: string;
  max_download: number;
  download_count: number;
  start_timestamp: string;
}

export interface BumpaStoreMeta {
  onBoard: BumpaOnBoardMeta;
  receipt: BumpaReceiptMeta;
  have_staff: string;
  about_bumpa: string | null;
  new_business: string;
  staff_number: number | null;
  themes_opt_in: boolean;
  has_customized: boolean;
  physical_store: string;
  business_sector: string;
  orders_per_week: string;
  instagram_handle: string;
  number_of_stores: number | null;
  reason_for_starting: string | null;
}

export interface BumpaPlan {
  id: number;
  name: string;
  plan_code: string;
  interval: string;
  description: string;
  status: string;
  amount: string;
  initial_amount: string;
  currency: string;
  is_trial: number;
  is_addon: number;
  type: string;
  slug: string;
  hierarchy: number;
  features: string[];
  created_at: string | null;
  updated_at: string;
}

export interface BumpaSubscription {
  id: number;
  store_id: number;
  interval: string;
  amount: string;
  start_date: string;
  ends_at: string;
  status: string;
  is_addon: number;
  channel: string;
  cancelled_at: string | null;
  plan_id: number;
  type: string | null;
  created_at: string;
  updated_at: string;
  has_subscribed: boolean;
  has_activated_trial: boolean;
  subscriptable_id: number | null;
  grace_period_ends_at: string;
  remaining_amount: number;
  no_of_extra_locations: number | null;
  no_of_extra_staffs: number | null;
  available_referral_amount: number;
  plan: BumpaPlan;
}

export interface BumpaStore {
  id: number;
  user_id: number;
  name: string;
  subdomain: string;
  domain: string | null;
  url: string;
  previous_url: string;
  phone: string;
  message_credits: string;
  purchased_messaging_credits: number;
  dialing_code: string;
  subtitle: string;
  description: string;
  logo: string;
  premium: boolean;
  custom: number;
  currency: string;
  template: string;
  address: BumpaStoreAddress;
  settings: BumpaStoreSettings;
  currencies: string | null;
  meta: BumpaStoreMeta;
  created_at: string;
  updated_at: string;
  paystack_id: string | null;
  paystack_code: string | null;
  card_brand: string | null;
  card_last_four: string | null;
  trial_ends_at: string | null;
  country_id: number;
  tx_fee: string;
  suspended: boolean;
  referral_code: string;
  flag: string | null;
  online: number;
  sender_id: string | null;
  deleted_at: string | null;
  store_category_id: number | null;
  business_category: string;
  default_location_id: number;
  nbc: number;
  nbc_locations: string | null;
  popup: number;
  popup_locations: string | null;
  business_name: string;
  has_activated_referral: number;
  image_path: string;
  address_formatted: string;
  logo_url: string;
  url_link: string;
  meta_description: string;
  default_no_of_staffs: number;
  default_no_of_locations: number;
  subscription: BumpaSubscription[];
  addon_subscriptions: unknown[];
  terminal: string | null;
  referral_link: string;
  phone_formatted: string;
  has_domain: boolean;
  has_web_orders: boolean;
  has_physical_orders: boolean;
  is_using_themes: boolean;
  kyc_level: number;
  has_ngn_wallet: boolean;
}

export interface BumpaLayoutPageSection {
  sc_view: string;
  sc_heading?: string;
  sc_subheading?: string;
  sc_form_placeholder?: string;
  sc_form_submit_text?: string;
  componentNewsletterForm?: unknown | null;
  sc_button_url?: string;
  sc_button_text?: string;
  componentBanner?: unknown | null;
  sc_banner_image?: string;
  sc_banner_title?: string;
  sc_banner_subtitle?: string;
  sc_banner_paragraph?: string;
  sc_title?: string;
  sc_subtitle?: string;
  sc_grid_items_count?: string;
  componentProductGrid?: unknown | null;
  sc_product_grid_items?: string;
}

export interface BumpaLayoutPages {
  index: {
    footer: BumpaLayoutPageSection[];
    header: BumpaLayoutPageSection[];
    content: BumpaLayoutPageSection[];
  };
}

export interface BumpaLayoutConfig {
  favicon: { url: string; status: boolean };
  about_us: { status: boolean };
  newsletter: { status: boolean };
  hero_banner: {
    banner_image: string;
    banner_title: string;
    banner_subtitle: string;
  };
  theme_color: { red: number; blue: number; green: number };
  social_links: unknown[];
  return_policy: { status: boolean };
  custom_message: { status: boolean };
  product_listing: string;
}

export interface BumpaLayout {
  id: number;
  store_id: number;
  pages: BumpaLayoutPages;
  config: BumpaLayoutConfig;
  css: string | null;
  head_scripts: string | null;
  body_scripts: string | null;
  created_at: string;
  updated_at: string;
  pixel_status: number;
  head_scripts_formatted: string;
}

export interface BumpaThemeOptions {
  contact: BumpaStoreAddress & { show: boolean };
  favicon: string;
  about_us: { show: boolean; title: string | null; content: string | null };
  whatsapp: { show: boolean };
  newsletter: { desc: string; show: boolean; title: string };
  theme_color: string;
  social_links: { show: boolean; links: { name: string; handle: string }[] };
  homepage_hero: {
    list: Array<{
      id: string;
      desc: string;
      media: string;
      title: string;
      customLink: string | null;
      customText: string | null;
      media_type: string;
    }>;
    show: boolean;
  };
  location_page: {
    show: boolean;
    details: { image: string | null; title: string | null; description: string | null };
  };
  return_policy: { show: boolean; content: string | null };
  product_listing: string;
  top_custom_message: { list: unknown[]; show: boolean };
}

export interface BumpaTheme {
  id: number;
  slug: string;
  category: string | null;
  name: string;
  secondary_image: string;
  price: string;
  description: string;
  preview_image: string;
  features: string[] | null;
  parent: string | null;
  is_custom: number;
  options: BumpaThemeOptions;
  active: boolean;
}

export interface BumpaStoreRoot {
  store: BumpaStore;
  layout: BumpaLayout;
  theme: BumpaTheme;
}

export interface BumpaProductImage {
  name: string;
  path: string;
  thumbnail_path: string;
}

export interface BumpaProductTagPivot {
  product_id: number;
  collection_id: number;
}

export interface BumpaProductOption {
  name: string;
  weight_kg: string | null;
  values: string[];
}

export interface BumpaProductImage {
  name: string;
  path: string;
  thumbnail_path: string;
}

export interface BumpaProductTagPivot {
  product_id: number;
  collection_id: number;
}

export interface BumpaProductTag {
  id: number;
  store_id: number;
  tag: string;
  created_at: string;
  updated_at: string;
  description: string | null;
  image_path: string | null;
  url: string;
  image_url: string;
  pivot?: BumpaProductTagPivot;
}

export interface BumpaCurrency {
  rate: string;
  default: boolean;
  currency: string;
  base_currency: number;
  currency_name: string;
  currency_symbol: string;
}

export interface BumpaLeanStore {
  id: number;
  currencies: BumpaCurrency[] | null;
  url: string;
}

export interface BumpaProduct {
  id: number;
  store_id: number;
  title: string;
  sku: string | null;
  description: string | null;
  unit: string;
  details: string | null;
  price: string | number;
  sales: string | null;
  stock: number;
  weight_kg: string | null;
  type: string;
  status: number;
  featured: boolean;
  manage_stock: boolean;
  sales_count: number;
  ratings_cache: string | null;
  ratings_count: number;
  options: BumpaProductOption[];
  image: string;
  images: BumpaProductImage[];
  files: unknown[];
  meta: unknown | null;
  currency_code: string;
  is_demo: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  product_category_id: number | null;
  source: string | null;
  source_id: number | null;
  use_multilocation: number;
  has_barcode: number;
  minimum_order_quantity: number;
  maximum_order_quantity: number | null;
  extras: unknown | null;
  quantity: string | number;
  min_selling_price: string;
  max_selling_price: string;
  min_discount_price: string | null;
  max_discount_price: string | null;
  max_discount_percentage: string | null;
  name: string;
  published: boolean;
  draft: boolean;
  slug: string;
  url: string;
  image_url: string;
  review_url: string;
  alt_image_url: string;
  price_formatted: string;
  formattedCreatedAt: string;
  sales_formatted: string;
  image_path: string;
  file_path: string;
  thumbnail_url: string;
  tag: BumpaProductTag;
  in_stock: boolean;
  variation_id: number | null;
  has_discount: boolean;
  barcode: string | null;
  variationCount: number;
  can_view_cost_price: boolean;
  tags: BumpaProductTag[];
  discounts: unknown[];
  preferred_barcode: string | null;
  lean_store: BumpaLeanStore;
  reserved_quantity?: number;
}

export interface BumpaPaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

export interface BumpaProductPagination {
  current_page: number;
  data: BumpaProduct[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: BumpaPaginationLink[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface BumpaProductsResponse {
  products: BumpaProductPagination;
}

export interface BumpaTagsResponse {
  tags: BumpaProductTag[];
}
