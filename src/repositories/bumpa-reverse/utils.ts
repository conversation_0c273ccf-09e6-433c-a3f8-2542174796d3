import { BumpaLayout, BumpaProduct, BumpaProductOption, BumpaProductTag, BumpaStore, BumpaTheme } from './types';
import { IStoreCategory, Store } from '../../modules/store/store.schema';
import { Item, ItemVariants, ThumbnailTypes } from '../../modules/item/item.schema';
import { COUNTRY_CODE, CURRENCIES } from '../../modules/country/country.schema';
import { BUSINESS_TYPES } from '../../modules/store/store.schema';
import { ITEM_SOURCE } from '../../enums/item.enum';

export function storeFromBumpa(
  bumpaStore: BumpaStore,
  bumpaTheme: BumpaTheme,
  bumpaLayout: BumpaLayout,
  tags: BumpaProductTag[] = [],
): Partial<Store> {
  const phoneNumber = bumpaStore.dialing_code + '-' + bumpaStore.phone.replace(bumpaStore.dialing_code, '');
  return {
    _id: bumpaStore.id.toString(),
    name: bumpaStore.name,
    description: bumpaStore.subtitle || bumpaStore.description,
    phone: phoneNumber,
    slug: bumpaStore.subdomain,
    primary_slug: bumpaStore.subdomain,
    slugs: [bumpaStore.subdomain],
    address: bumpaStore.address?.street || null,
    state: bumpaStore.address?.state || '',
    country: COUNTRY_CODE.NG, // Assuming Bumpa is Nigeria-focused
    logo: bumpaStore.logo_url || '',
    hero_image: bumpaTheme.options?.homepage_hero?.list[0]?.media || '',
    custom_message: bumpaLayout?.config?.hero_banner?.banner_title || '',
    currencies: {
      default: CURRENCIES.NGN,
      products: CURRENCIES.NGN,
      storefront: [CURRENCIES.NGN],
      storefront_default: CURRENCIES.NGN,
      rates: null,
    },
    payments_enabled: false,
    payment_options: {},
    configuration: {
      view_modes: {
        grid: true,
        card: false,
        horizontal: false,
        default: 'grid',
      },
      color: bumpaTheme?.options?.theme_color || null,
      automated_delivery_delay: 0,
      payment_timeout: 0,
      require_emails: false,
      pass_chowbot_fee_to_deliveries: false,
      pickup_address: null,
      custom_message: bumpaLayout?.config?.hero_banner?.banner_title || '',
      ga_id: null,
      fb_pixel: null,
      bot_initiation_message: null,
      custom_order_success_message: null,
      enquiry_message: null,
      sort_by_latest_products: false,
      show_unavailable_products: false,
      whatsapp_checkout_enabled: false,
      facebook_pixel_enabled: false,
      facebook_pixel_enabled_cache: null,
      customer_pickup_enabled: false,
      require_delivery_info: false,
      confirm_order_before_payment: false,
      average_delivery_timeline: 0,
      store_welcome_message: bumpaTheme.options?.homepage_hero?.list[0]?.title || '',
      direct_checkout_enabled: false,
    },
    delivery_areas: [],
    extra_info: {
      delivery_timeline: null,
      production_timeline: null,
      refund_policy: bumpaTheme?.options?.return_policy?.content || '',
      images: [],
      images_label: '',
    },
    business_category: {
      name: bumpaStore.business_category || '',
      type: bumpaStore.meta?.physical_store === 'Yes' ? BUSINESS_TYPES.PHYSICAL : BUSINESS_TYPES.DIGITAL,
      monthly_orders: bumpaStore.meta?.orders_per_week || '0-10',
      product_types: [],
    },
    meta: {
      instagram_item_upload_count: 0,
    },
    socials: {
      facebook:
        bumpaStore.settings?.social?.facebook ||
        bumpaTheme?.options?.social_links?.links?.find((link) => link.name === 'facebook')?.handle ||
        null,
      instagram:
        bumpaStore.settings?.social?.instagram ||
        bumpaTheme?.options?.social_links?.links?.find((link) => link.name === 'instagram')?.handle ||
        null,
      twitter:
        bumpaStore.settings?.social?.twitter ||
        bumpaTheme?.options?.social_links?.links?.find((link) => link.name === 'twitter')?.handle ||
        null,
      tiktok:
        bumpaStore.settings?.social?.tiktok ||
        bumpaTheme?.options?.social_links?.links?.find((link) => link.name === 'tiktok')?.handle ||
        null,
      snapchat:
        bumpaStore.settings?.social?.snapchat ||
        bumpaTheme?.options?.social_links?.links?.find((link) => link.name === 'snapchat')?.handle ||
        null,
      whatsapp: null,
    },
    categories: tags.map((tag) => bumpaProductTagToItemTag(tag)),
  };
}

export function productFromBumpa(bumpaProduct: BumpaProduct): Partial<Item> {
  const basePrice = parseFloat(
    bumpaProduct.price ? bumpaProduct.price.toString() : bumpaProduct?.max_selling_price.toString(),
  );
  const variantOptions = getOptionCombinations(bumpaProduct.options, basePrice);

  console.log('<===== PRODUCT IMAGES =====>');
  const images = bumpaProduct.images.map((img) => 'https://dodptt9f4zk9h.cloudfront.net' + img.path);
  console.log({ images });

  return {
    name: bumpaProduct.title,
    price: basePrice,
    price_unit: bumpaProduct?.unit,
    description: bumpaProduct.description,
    slug: bumpaProduct.slug,
    available: bumpaProduct.status === 1 && bumpaProduct.in_stock,
    images: images.length > 0 ? images : [bumpaProduct.image_url],
    thumbnail: 0,
    thumbnail_type: ThumbnailTypes.IMAGE,
    featuredImage: bumpaProduct.image_url,
    views: 0,
    total_orders: bumpaProduct.sales_count,
    is_deleted: !!bumpaProduct.deleted_at,
    quantity: bumpaProduct.stock || undefined,
    minimum_order_quantity: bumpaProduct.minimum_order_quantity,
    weight: bumpaProduct.weight_kg ? parseFloat(bumpaProduct.weight_kg) : undefined,
    is_featured: bumpaProduct.featured,
    upload_source: ITEM_SOURCE.MANUAL,
    category: bumpaProduct.tag ? bumpaProductTagToItemTag(bumpaProduct.tag) : null,
    tags: bumpaProduct.tags.map((tag) => bumpaProductTagToItemTag(tag)),
    meta: {
      sku: bumpaProduct.sku,
      unit: bumpaProduct.unit,
      barcode: bumpaProduct.barcode,
      source_id: bumpaProduct.source_id?.toString(),
      source: 'bumpa',
    },
    variants: {
      type: 'custom',
      options: variantOptions,
    },
  };
}

export function bumpaProductTagToItemTag(bumpaProductTag: BumpaProductTag): IStoreCategory {
  return {
    name: bumpaProductTag.tag,
    emoji: '',
    id: bumpaProductTag.id.toString(),
    _id: bumpaProductTag.id.toString(),
  };
}

export function getOptionCombinations(options: BumpaProductOption[], basePrice: number): ItemVariants['options'] {
  if (options.length === 0) return [];

  // Start with an empty combination
  let combinations: { [key: string]: string }[] = [{}];

  for (const option of options) {
    const newCombinations: { [key: string]: string }[] = [];

    for (const combination of combinations) {
      for (const value of option.values) {
        newCombinations.push({
          ...combination,
          [option.name]: value,
        });
      }
    }

    combinations = newCombinations;
  }

  return combinations.map((c) => ({ values: c, price: basePrice, is_available: true, _id: '' }));
}
