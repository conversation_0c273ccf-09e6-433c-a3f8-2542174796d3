import { Injectable, Logger } from '@nestjs/common';
import Axios, { AxiosInstance } from 'axios';
import { extractErrorDetails } from '../../utils/axios-errors';
import { registerErrors } from '../../utils/errors.util';
import { BumpaProductsResponse, BumpaStoreRoot, BumpaTagsResponse } from './types';

function normalizeBaseUrl(baseUrl: string): string {
  if (!baseUrl) return '';
  const withProtocol = /^https?:\/\//i.test(baseUrl) ? baseUrl : `https://${baseUrl}`;
  return withProtocol.replace(/\/$/, '');
}

@Injectable()
export class BumpaReverseRepository {
  constructor(private readonly logger: Logger) {}

  private createAxios(baseUrl: string): AxiosInstance {
    const normalized = normalizeBaseUrl(baseUrl);
    return Axios.create({
      baseURL: normalized,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  public async getCollections(baseUrl: string): Promise<{ data?: BumpaTagsResponse; error?: any }> {
    try {
      const axios = this.createAxios(baseUrl);
      const res = await axios.get('/storefront/collections');
      return { data: res?.data as BumpaTagsResponse };
    } catch (e) {
      const error = extractErrorDetails(e);
      this.logger.log(error);
      registerErrors(error);
      return { error };
    }
  }

  public async getStore(baseUrl: string): Promise<{ data?: BumpaStoreRoot; error?: any }> {
    try {
      const axios = this.createAxios(baseUrl);
      const res = await axios.get('/storefront/store');
      return { data: res?.data as BumpaStoreRoot };
    } catch (e) {
      const error = extractErrorDetails(e);
      this.logger.log(error);
      registerErrors(error);
      return { error };
    }
  }

  public async getProducts(
    baseUrl: string,
    query?: Partial<{
      min_stock: number;
      limit: number;
      page: number;
      sort_by: string;
      variations: boolean;
      status: number;
      search: string;
    }>,
  ): Promise<{ data?: BumpaProductsResponse; error?: any }> {
    try {
      const axios = this.createAxios(baseUrl);
      const params = {
        min_stock: 0,
        limit: query?.limit || 15,
        page: query?.page,
        sort_by: 'new_products',
        variations: false,
        status: 1,
        search: query?.search,
        ...(query || {}),
      };
      const res = await axios.get('/storefront/products', { params });
      return { data: res?.data as BumpaProductsResponse };
    } catch (e) {
      const error = extractErrorDetails(e);
      this.logger.log(error);
      registerErrors(error);
      return { error };
    }
  }
}
