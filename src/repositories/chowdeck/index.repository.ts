import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Axios, { AxiosInstance } from 'axios';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { ChowdeckConfig } from '../../config/types/chowdeck.config';
import { registerErrors } from '../../utils/errors.util';
import { CreateChowdeckMerchantPayload, CreateDeliveryPayload, GetDeliveryFeesPayload } from './types';
import { extractErrorDetails } from '../../utils/axios-errors';
import axios from 'axios';
import { Cache } from 'cache-manager';

@Injectable()
export class ChowdeckRepository {
  private config: ChowdeckConfig;
  private axios: AxiosInstance;
  private dashboard: AxiosInstance;

  constructor(
    private readonly logger: Logger,
    private readonly configService: ConfigService,
    private readonly brokerTransport: BrokerTransportService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
  ) {
    this.config = configService.get<ChowdeckConfig>('chowdeckConfiguration');

    this.axios = Axios.create({
      baseURL: 'https://api.chowdeck.com',
      // baseURL: 'https://api.chowdeck.studio',
      headers: {
        Authorization: `Bearer ${this.config?.apiToken}`,
        'Content-Type': 'application/json',
      },
    });

    this.loginToDashboard();
  }

  public createAxiosInstance(token: string) {
    return Axios.create({
      baseURL: 'https://api.chowdeck.com',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
  }

  public async loginToDashboard() {
    try {
      let token: string = await this.cacheManager.get('chowdeck_dashboard_token');

      if (!token) {
        const res = await Axios.create({
          baseURL: 'https://api.chowdeck.com/auth/login',
          headers: {
            'Content-Type': 'application/json',
          },
        }).post('', {
          email: this.config?.dashboardEmail,
          password: this.config?.dashboardPassword,
          role: 'vendor',
        });

        token = res?.data?.data?.token;
        this.cacheManager.set('chowdeck_dashboard_token', token, { ttl: 60 * 60 * 24 * 30 }); // 30 days
      }

      if (token) {
        this.config.dashboardToken = token;
        this.dashboard = Axios.create({
          baseURL: 'https://api.chowdeck.com',
          headers: {
            Authorization: `Bearer ${this.config?.dashboardToken}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (e) {}
  }

  public async createMerchant(merchantPayload: CreateChowdeckMerchantPayload) {
    try {
      const res = await this.axios.post(`/merchant`, merchantPayload);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async acceptSingleOrder(vendorRef: string, orderRef: string, access_token: string) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.put(`/merchant/${vendorRef}/order/${orderRef}/accept`);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async rejectSingleOrder(vendorRef: string, orderRef: string, access_token: string) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.put(`/merchant/${vendorRef}/order/${orderRef}/reject`);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async getMenuCategories(vendorRef: string, access_token: string) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.get(`/merchant/${vendorRef}/menucategory`);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async createMenuCategory(vendorRef: string, reference: string, name: string, access_token: string) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const apiData = {
        name,
        reference,
      };

      const res = await _axios.post(`/merchant/${vendorRef}/menucategory`, apiData);
      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async updateMenuCategory(
    vendorRef: string,
    categoryRef: string,
    update: { name: string; is_published: boolean; rank: number },
    access_token: string,
  ) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.put(`/merchant/${vendorRef}/menucategory/${categoryRef}`, update);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async getMenuItems(vendorRef: string, access_token: string) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.get(`/merchant/${vendorRef}/menu`);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async getMenuItem(vendorRef: string, menuRef, access_token: string) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.get(`/merchant/${vendorRef}/menu/${menuRef}`);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async createMenuItem(
    vendorRef: string,
    itemData: {
      reference: string;
      name: string;
      description: string;
      menu_category_id: number;
      in_stock: boolean;
      price: number;
      images: {
        path: string;
        rank: number;
      }[];
    },
    access_token: string,
  ) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.post(`/merchant/${vendorRef}/menu`, itemData);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async updateMenuItem(
    vendorRef: string,
    itemRef: string,
    itemData: {
      name?: string;
      description?: string;
      menu_category_id?: number;
      in_stock?: boolean;
      price?: number;
      price_description?: string;
      rank?: number;
      is_published?: boolean;
      images?: {
        path: string;
        rank: number;
      }[];
    },
    access_token: string,
  ) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.put(`/merchant/${vendorRef}/menu/${itemRef}`, itemData);

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async bulkCreateItems(
    vendorRef: string,
    itemData: {
      reference: string;
      name: string;
      category?: {
        name: string;
        rank: number;
        reference: string;
      };
      description: string;
      menu_category_id?: number;
      in_stock: boolean;
      price: number;
      images: {
        path: string;
        rank: number;
      }[];
      modifiers?: {
        name: string;
        items: {
          name: string;
          price: number;
          reference: string;
        }[];
      }[];
    }[],
    access_token: string,
  ) {
    try {
      const _axios = access_token ? this.createAxiosInstance(access_token) : this.axios;
      const res = await _axios.post(`/merchant/${vendorRef}/menu/bulk-upload`, { items: itemData });

      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async getDeliveryFee(deliveryFeeData: GetDeliveryFeesPayload) {
    try {
      const reference = this.config?.catlog_merchant_reference;
      const res = await this.axios.post(`merchant/${reference}/delivery/fee`, deliveryFeeData);
      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async initiateDelivery(deliveryData: CreateDeliveryPayload) {
    try {
      const reference = this.config?.catlog_merchant_reference;
      const res = await this.axios.post(`merchant/${reference}/delivery`, deliveryData);
      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      console.log('<==== CHOWDECK DELIVERY INITIATION ERROR =====>');
      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async getDeliveryInfo(deliveryId: string) {
    try {
      const res = await this.dashboard.get(`vendor/order/${deliveryId}`);
      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error: error };
    }
  }

  public async cancelDelivery(deliveryId: string) {
    try {
      const res = await this.dashboard.post(`vendor/order/${deliveryId}/reject`, {
        reason: 'The order is not available at the moment',
      });
      return { data: res?.data?.data };
    } catch (e) {
      const error = extractErrorDetails(e);

      this.logger.log(error);
      registerErrors(error);
      return { error };
    }
  }
}
