import {
  Body,
  Column,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
import config from '../tailwind.config';
import EmailButton from './components/button';
import EmailFonts from './components/fonts';
import EmailHeader2 from './components/header-2';
import React from 'react';
import { toCurrency } from './utils/functions';
import { EmailOrderData } from './utils/types';
import OrderDetails, { CustomerDetails, DeliveryDetails } from './components/orders/order-details';

const EmailTemplate = (props: EmailOrderData) => {
  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader2
                headerImage={props.store_logo}
                avatarPlaceholderChar={props.store_name[0]}
                textColor={props.store_color}
              >
                We have Received <br /> Your Order! 🎉
              </EmailHeader2>
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.name},
                  </Heading>
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    Thank you for shopping with{' '}
                    <span className="text-black-secondary font-semibold font-body" style={{ color: props.store_color }}>
                      {props.store_name}!
                    </span>
                    , We’ve received your order and it's currently processing.{' '}
                    <a href={props.order.payment_link} className="text-black-secondary font-semibold font-body">
                      You can make payment here.
                    </a>
                  </Text>
                  <Text className="text-grey-subtext font-body text-sm sm:text-base">
                    Here’s a summary of your order:
                  </Text>

                  <OrderDetails {...props} />

                  {props.order?.delivery_address && <DeliveryDetails {...props} />}

                  <EmailButton
                    href={props.order.link}
                    style={{ backgroundColor: props.store_color }}
                    className="my-6.25"
                  >
                    View full Order Information
                  </EmailButton>
                  <Hr />
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or complaints regarding this order, please reach out to us on{' '}
                    <Link
                      href={`https://wa.me/${props.store_phone}`}
                      style={{ color: props.store_color }}
                      className="font-medium font-body"
                    >
                      <EMAIL>
                    </Link>
                  </Text>
                </Column>
              </Row>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  preview_text: 'Your order from TechGadgets is pending!',
  store_name: 'TechGadgets',
  name: 'Emily Johnson',
  store_color: '#FF33A2',
  store_logo: null,
  order: {
    order_id: 'CLGO-6299282',
    link: 'https://example.com/orders/e349f216-48c5-4ef9-b712-45d0fee5b70d',
    payment_link: 'https://example.com/pay/e349f216-48c5-4ef9-b712-45d0fee5b70d?byCustomer=true',
    order_status: 'Pending',
    products: [
      {
        name: 'Backpack',
        price: '261',
        quantity: 3,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/4tpefumup.jpeg',
      },
      {
        name: 'Laptop',
        price: '486',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/r9xxull2i9.jpeg',
      },
      {
        name: 'Sneakers',
        price: '275',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/uv2h48dfy9.jpeg',
      },
      {
        name: 'Smartphone',
        price: '357',
        quantity: 4,
        image: 'https://catlog-s3.s3.eu-west-2.amazonaws.com/ITEMS/b3hbmxxxem.jpeg',
      },
    ],
    currency: 'NGN',
    total: '5255',
    delivery_method: 'Delivery',
    delivery_area: 'Lekki',
    delivery_address: 'No 2, Oba Dosumu Street, Lekki Phase 1, Lagos',
    fees: [
      {
        label: 'Coupon Code',
        amount: 19,
      },
    ],
    customer_info: {
      name: 'Emily Johnson',
      email: null,
      phone: '+234-80722696716',
    },
  },
} as EmailOrderData;

export default EmailTemplate;
