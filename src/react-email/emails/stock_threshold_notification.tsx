import {
  <PERSON>,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import config from "../tailwind.config";
import EmailButton from "./components/button";
import EmailFonts from "./components/fonts";
import EmailFooter from "./components/footer";
import * as React from "react";
import EmailHeader from "./components/header";

interface Props {
  preview_text?: string;
  store_name: string;
  owner_name: string;
  item_name: string;
  variant_name?: string;
  current_quantity: number;
  threshold: number;
  is_out_of_stock: boolean;
  dashboard_link: string;
}

const EmailTemplate = (props: Props) => {
  const itemDisplayName = props.variant_name 
    ? `${props.item_name} (${props.variant_name})`
    : props.item_name;

  const alertType = props.is_out_of_stock ? "Out of Stock" : "Low Stock";
  const alertMessage = props.is_out_of_stock
    ? `${itemDisplayName} is now completely out of stock.`
    : `${itemDisplayName} is running low on stock with only ${props.current_quantity} units remaining (threshold: ${props.threshold}).`;

  return (
    <Html>
      <Tailwind
        config={
          {
            theme: config.theme,
          } as any
        }
      >
        <Head>
          <EmailFonts />
        </Head>
        {props.preview_text && <Preview>{props.preview_text}</Preview>}

        <Body className="bg-grey-fields-100 pb-[70px] !font-body">
          <Container className="max-w-[680px] bg-white pb-[55px]">
            <Section>
              <EmailHeader type="stock_threshold_notification" />
              <Row>
                <Column className="pt-[20px] sm:pt-[50px] px-[7%] sm:px-[12%] pb-0">
                  <Heading as="h2" className="text-black text-xl sm:text-2xl">
                    Hi {props.owner_name}!
                  </Heading>
                  
                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    We wanted to alert you about a stock issue in your store{" "}
                    <span className="text-black-secondary font-semibold font-body">
                      {props.store_name}
                    </span>.
                  </Text>

                  <div className={`p-4 rounded-lg mb-6 ${
                    props.is_out_of_stock 
                      ? 'bg-red-50 border border-red-200' 
                      : 'bg-yellow-50 border border-yellow-200'
                  }`}>
                    <Text className={`font-body font-semibold text-sm sm:text-base mb-2 ${
                      props.is_out_of_stock ? 'text-red-800' : 'text-yellow-800'
                    }`}>
                      🚨 {alertType} Alert
                    </Text>
                    <Text className={`font-body text-sm sm:text-base ${
                      props.is_out_of_stock ? 'text-red-700' : 'text-yellow-700'
                    }`}>
                      {alertMessage}
                    </Text>
                  </div>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    {props.is_out_of_stock 
                      ? "Please restock this item as soon as possible to avoid losing potential sales."
                      : "Consider restocking this item soon to ensure you don't run out of inventory."
                    }
                  </Text>

                  <Text className="font-body text-grey-subtext text-sm sm:text-base">
                    You can update your inventory quantities and stock thresholds from your dashboard.
                  </Text>

                  <EmailButton href={props.dashboard_link} className="my-6.25">
                    Manage Inventory
                  </EmailButton>

                  <Hr />
                  
                  <Text className="font-body text-grey-subtext text-xs sm:text-sm">
                    <strong>Item Details:</strong><br />
                    • Product: {props.item_name}<br />
                    {props.variant_name && (
                      <>• Variant: {props.variant_name}<br /></>
                    )}
                    • Current Stock: {props.current_quantity} units<br />
                    • Stock Threshold: {props.threshold} units<br />
                    • Store: {props.store_name}
                  </Text>

                  <Hr />
                  
                  <Text className="font-body text-grey-subtext">
                    If you have any questions or need help managing your inventory, please reach out to us on{" "}
                    <Link href="mailto:<EMAIL>" className="text-primary-500 font-medium font-body">
                      <EMAIL>
                    </Link>
                  </Text>
                  <Hr />
                </Column>
              </Row>
              <EmailFooter bannerType="light-yellow" />
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

EmailTemplate.PreviewProps = {
  store_name: "Ashluxe Store",
  owner_name: "John Doe",
  item_name: "Premium T-Shirt",
  variant_name: "Size: L, Color: Blue",
  current_quantity: 2,
  threshold: 5,
  is_out_of_stock: false,
  dashboard_link: "https://catlog.shop/dashboard/items",
} as Props;

export default EmailTemplate;
