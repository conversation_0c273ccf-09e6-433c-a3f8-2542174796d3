import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import {
  IsArray,
  isArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  isFQDN,
  IsIn,
  isIn,
  IsNotEmpty,
  IsNumber,
  isObject,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
  IsUUID,
  ArrayMinSize,
  IsUrl,
  Min,
} from 'class-validator';
import { Invite } from '../../modules/store/teams/invites.schema';
import { Item } from '../../modules/item/item.schema';
import {
  BUSINESS_TYPES,
  FAQ,
  INFO_BLOCK_CONTENT_TYPE,
  STORE_TYPES,
  Store,
  StorePaymentMethod,
  STOREFRONT_VERSION,
} from '../../modules/store/store.schema';
import { Order } from '../../modules/orders/order.schema';
import { STORE_ROLES } from '../../utils/permissions.util';
import { COUNTRY_CODE, CURRENCIES, CurrencyMap } from '../../modules/country/country.schema';
import { PAYMENT_METHODS } from '../../enums/payment.enum';
import { Type } from 'class-transformer';
import { PLAN_TYPE } from '../../enums/plan.enum';
import { Testimonial } from '../../modules/store/store.schema';

export class CreateStoreDto {
  @ApiProperty({ example: 'Ashluxe' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  country: COUNTRY_CODE;

  @ApiProperty()
  @IsString()
  @IsOptional()
  logo: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  store_type: STORE_TYPES;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  copy_config?: boolean;
}

export class CreateStoreDtoV2 {
  @ApiProperty({ example: 'Ashluxe' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  country: COUNTRY_CODE;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  store_type: STORE_TYPES;

  @ApiProperty()
  @IsOptional()
  slug?: string;
}

export class UpdateTeamMemberDto {
  @ApiProperty()
  invite_id?: string;

  @ApiProperty()
  user_id?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  role: STORE_ROLES;
}

export class CreateInviteDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  role: STORE_ROLES;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  phone?: string;
}
export class AcceptInviteDto {
  @ApiProperty()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  password?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  phone?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  user?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  recaptcha_token?: string;
}

export class UpdateStoreDto extends PickType(Store, [
  'logo',
  'hero_image',
  'socials',
  'custom_message',
  'configuration',
  'third_party_configs',
  'onboarding_steps',
  'deliveries_enabled',
  'extra_info',
  'business_category',
  'secondary_phone',
]) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  phone: string;

  @ApiProperty()
  @IsString()
  // @IsNotEmpty()
  @IsOptional()
  delivery_locations: string;

  @ApiProperty()
  // @Transform((address: string) => address === "" ? undefined : address)
  @IsString()
  // @IsNotEmpty()
  @IsOptional()
  address: string;

  @ApiProperty()
  // @Transform((state: string) => state === "" ? undefined : state)
  @IsString()
  // @IsNotEmpty()
  @IsOptional()
  state: string;

  @ApiProperty()
  // @Transform((country: string) => country === "" ? undefined : country)
  @IsString()
  // @IsNotEmpty()
  @IsOptional()
  country: COUNTRY_CODE;

  @IsString()
  @IsOptional()
  pickup_address: string;
}

export class ImportItemsDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  store_id: string;

  @ApiProperty()
  items: string[];
}

export class AddOrUpdateStoreCategoryStoreDto {
  _id: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  id: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  emoji: string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  meta?: {
    chowdeck?: {
      id?: number;
      reference?: string;
    };
  };
}

export class AdditionalStoreDetailsDto {
  @ApiProperty({
    description: 'The type of business',
    enum: BUSINESS_TYPES,
    example: BUSINESS_TYPES.PHYSICAL,
  })
  @IsEnum(BUSINESS_TYPES, { message: 'business_type must be a valid BUSINESS_TYPES value' })
  business_type: BUSINESS_TYPES;

  @ApiProperty({
    description: 'Estimated monthly number of orders',
    example: '100-500',
  })
  @IsOptional()
  @IsString({ message: 'Monthly orders must be a string' })
  monthly_orders: string;

  @ApiProperty({
    description: 'The category of the business',
    example: 'Fashion',
  })
  @IsNotEmpty({ message: 'business_category must not be empty' })
  @IsString({ message: 'business_category must be a string' })
  business_category: string;

  @ApiProperty({
    description: 'Description of the business',
    example: 'We Sell Clothes',
  })
  @IsNotEmpty({ message: 'Description must not be empty' })
  @IsString({ message: 'Description must be a string' })
  description: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  logo: string;

  @ApiProperty({
    description: 'The primary social media platform used',
    example: 'Instagram',
  })
  @IsNotEmpty()
  @IsString({ message: 'social_media_platform must be a string' })
  social_media_platform: string;

  @ApiProperty({
    description: 'The username for the social media account',
    example: '@business_handle',
  })
  @IsNotEmpty()
  @IsString({ message: 'social_media_username must be a string' })
  social_media_username: string;
}

export class StoreCategoryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  emoji: string;
}

export class StoreCategoriesResponseDto {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: StoreCategoryDto })
  data: StoreCategoryDto;
}

export class StoreStatisticsResponse {
  @ApiProperty()
  total_visits: number;

  @ApiProperty()
  total_carts: number;

  @ApiProperty()
  total_items: number;

  @ApiProperty()
  visits: Array<{ id: string; time: string }>;
}

export class StoreTopItemsResponse {
  @ApiProperty()
  top_items: Array<Item>;
}

export class StoreResponseDto {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: Store })
  data: Store;
}

export class InviteResponseDto {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: Store })
  data: Invite;
}
export class ImportItemsResponseDto {
  @ApiProperty()
  message: string;

  @ApiProperty({ type: Store })
  data: Store;
}

export class UpdateStoreSlugDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  slug: string;
}

export class UpdateDirectCheckoutDto {
  @ApiProperty()
  @IsBoolean()
  direct_checkout_enabled: boolean;
}
export class UpdateSecurityPinDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  new_pin: string;

  // @ApiProperty({ required: false })
  // @IsString()
  // current_pin?: string;
}

export class ToggleLowStockNotificationsDto {
  @ApiProperty({ required: true })
  @IsBoolean()
  @IsNotEmpty()
  enabled: boolean;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  threshold?: number;
}

export class UpdateCurrenciesDto {
  @ApiProperty({
    type: 'string',
    enum: CURRENCIES,
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(CURRENCIES)
  products: CURRENCIES;

  @ApiProperty({
    type: 'string',
    enum: CURRENCIES,
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(CURRENCIES)
  storefront_default: CURRENCIES;

  @IsNotEmpty()
  @IsArray()
  // @ValidateNested({ each: true })
  @Type(() => String)
  @IsEnum(CURRENCIES, { each: true })
  storefront: CURRENCIES[];

  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  rates: CurrencyMap;
}

export class UpdateChowdeckSettingsDto {
  @ApiProperty({ required: true })
  @IsBoolean()
  @IsNotEmpty()
  sync_to_chowdeck: boolean;

  @ApiProperty({ required: true })
  @IsBoolean()
  @IsNotEmpty()
  sync_from_chowdeck: boolean;

  @ApiProperty({ required: true })
  @IsBoolean()
  @IsNotEmpty()
  auto_delivery: boolean;

  @ApiProperty({ required: true })
  @IsString()
  @IsOptional()
  pickup_address?: string;
}

export class DeleteDeliveryAreaDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  public id: string;
}

export class CreateDeliveryAreaDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  public name: string;

  @ApiProperty({ required: true })
  @IsNumber()
  @IsNotEmpty()
  public fee: number;
}

export class UpdateDeliveryAreaDto extends CreateDeliveryAreaDto {
  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  public id: string;
}
export class UpdateStoreMaintenanceModeDto {
  @ApiProperty({ required: true })
  @IsBoolean()
  @IsNotEmpty()
  state: boolean;
}

export class UpdateStorePaymentMethodsDto {
  @IsArray()
  @ValidateNested()
  @Type(() => StorePaymentMethod)
  payment_options: StorePaymentMethod[];

  @IsString()
  @IsNotEmpty()
  currency: CURRENCIES;
}

export class FilterStoreDto {
  @ApiProperty({ required: false })
  country: 'NG' | 'GH';

  @ApiProperty({ required: false })
  search: string;

  @ApiProperty({ required: false })
  plan: PLAN_TYPE;
}

export class FilterStoreAnalysisDto {
  @ApiProperty({ required: false })
  country: COUNTRY_CODE;

  @ApiProperty({ required: false })
  search: string;
}

export class ExportItemsToChowdeckDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsArray()
  items: string[];
}

export class SyncItemsWithChowdeckDto {
  @ApiProperty({ required: true })
  @IsBoolean()
  @IsNotEmpty()
  from_chowdeck: boolean;
}

export class ImportChowdeckItemsDto {
  @ApiProperty({ required: true, type: [Number] })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  items: number[];
}

export class CategoryWithFirstItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  emoji: string;

  @ApiProperty()
  items_count: number;

  @ApiProperty()
  first_item?: {
    id: string;
    name: string;
    price: number;
    images: string[];
    description: string;
    available: boolean;
  };
}

export class CategoriesWithFirstItemResponseDto {
  @ApiProperty({ type: [CategoryWithFirstItemDto] })
  data: CategoryWithFirstItemDto[];

  @ApiProperty()
  message: string;
}

export class CreateTestimonialDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  customer_name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;
}

export class UpdateTestimonialDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  customer_name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;
}

export class TestimonialResponseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  customer_name: string;

  @ApiProperty()
  content: string;

  @ApiProperty({ required: false })
  source?: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  is_visible: boolean;
}

export class AboutUsContentDto {
  @ApiPropertyOptional({ description: 'The main content text for the About Us section' })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({ description: 'Array of image URLs for the About Us section', type: [String] })
  @IsOptional()
  @IsArray()
  images?: string[];
}

export class TestimonialDto {
  @ApiPropertyOptional({ description: 'The unique ID of the testimonial (auto-generated for new testimonials)' })
  @IsOptional()
  @IsString()
  _id?: string;

  @ApiProperty({ description: 'Name of the customer who provided the testimonial' })
  @IsString()
  @IsNotEmpty()
  customer_name: string;

  @ApiProperty({ description: 'The testimonial content/review text' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiPropertyOptional({ description: 'Source of the testimonial (e.g., Instagram, Facebook)' })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({ description: 'Whether the testimonial should be visible on the store', default: true })
  @IsOptional()
  @IsBoolean()
  is_visible?: boolean;

  @ApiPropertyOptional({ description: 'Date when the testimonial was created' })
  @IsOptional()
  created_at?: Date;
}

export class StoreAboutContentDto {
  @ApiPropertyOptional({ description: 'About Us section content', type: AboutUsContentDto })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AboutUsContentDto)
  about_us?: AboutUsContentDto;

  @ApiPropertyOptional({ description: 'Array of customer testimonials', type: [TestimonialDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TestimonialDto)
  testimonials?: TestimonialDto[];
}

export class StoreAboutContentResponseDto {
  @ApiProperty({ description: 'About Us section content', type: AboutUsContentDto })
  about_us: AboutUsContentDto;

  @ApiProperty({ description: 'Array of customer testimonials', type: [TestimonialDto] })
  testimonials: TestimonialDto[];
}

export class CreateFAQDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  answer: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;
}

export class UpdateFAQDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  question?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  answer?: string;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;
}

export class FAQResponseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  question: string;

  @ApiProperty()
  answer: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  is_visible: boolean;
}

export class FAQDto {
  @ApiPropertyOptional({ description: 'The unique ID of the FAQ (auto-generated for new FAQs)' })
  @IsOptional()
  @IsString()
  _id?: string;

  @ApiProperty({ description: 'The question text' })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({ description: 'The answer text' })
  @IsString()
  @IsNotEmpty()
  answer: string;

  @ApiPropertyOptional({ description: 'Whether the FAQ should be visible on the store', default: true })
  @IsOptional()
  @IsBoolean()
  is_visible?: boolean;

  @ApiPropertyOptional({ description: 'Date when the FAQ was created' })
  @IsOptional()
  created_at?: Date;
}

export class StoreFAQContentDto {
  @ApiPropertyOptional({ description: 'Array of FAQs', type: [FAQDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FAQDto)
  faqs?: FAQDto[];
}

export class StoreFAQContentResponseDto {
  @ApiProperty({ description: 'Array of FAQs', type: [FAQDto] })
  faqs: FAQDto[];
}

export class CreateInfoBlockDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ enum: INFO_BLOCK_CONTENT_TYPE })
  @IsEnum(INFO_BLOCK_CONTENT_TYPE)
  @IsNotEmpty()
  content_type: INFO_BLOCK_CONTENT_TYPE;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  text_content?: string;

  @ApiProperty({ required: false, type: [String] })
  @IsArray()
  @IsOptional()
  image_content?: string[];

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  tag?: string;
}

export class UpdateInfoBlockDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ required: false, enum: INFO_BLOCK_CONTENT_TYPE })
  @IsEnum(INFO_BLOCK_CONTENT_TYPE)
  @IsOptional()
  content_type?: INFO_BLOCK_CONTENT_TYPE;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  text_content?: string;

  @ApiProperty({ required: false, type: [String] })
  @IsArray()
  @IsOptional()
  image_content?: string[];

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  is_visible?: boolean;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  tag?: string;
}

export class InfoBlockResponseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty({ enum: INFO_BLOCK_CONTENT_TYPE })
  content_type: INFO_BLOCK_CONTENT_TYPE;

  @ApiProperty({ required: false })
  text_content?: string;

  @ApiProperty({ required: false, type: [String] })
  image_content?: string[];

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  is_visible: boolean;

  @ApiProperty({ required: false })
  tag?: string;
}

export class InfoBlockDto {
  @ApiPropertyOptional({ description: 'The unique ID of the info block (auto-generated for new info blocks)' })
  @IsOptional()
  @IsString()
  _id?: string;

  @ApiProperty({ description: 'The title of the info block' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'The content type of the info block', enum: INFO_BLOCK_CONTENT_TYPE })
  @IsEnum(INFO_BLOCK_CONTENT_TYPE)
  @IsNotEmpty()
  content_type: INFO_BLOCK_CONTENT_TYPE;

  @ApiPropertyOptional({ description: 'The text content (used when content_type is TEXT)' })
  @IsOptional()
  @IsString()
  text_content?: string;

  @ApiPropertyOptional({ description: 'The image content (used when content_type is IMAGES)', type: [String] })
  @IsOptional()
  @IsArray()
  image_content?: string[];

  @ApiPropertyOptional({ description: 'Whether the info block should be visible on the store', default: true })
  @IsOptional()
  @IsBoolean()
  is_visible?: boolean;

  @ApiPropertyOptional({ description: 'Date when the info block was created' })
  @IsOptional()
  created_at?: Date;

  @ApiPropertyOptional({ description: 'Optional tag for categorizing info blocks' })
  @IsOptional()
  @IsString()
  tag?: string;
}

export class StoreInfoBlockContentDto {
  @ApiPropertyOptional({ description: 'Array of info blocks', type: [InfoBlockDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => InfoBlockDto)
  info_blocks?: InfoBlockDto[];
}

export class StoreInfoBlockContentResponseDto {
  @ApiProperty({ description: 'Array of info blocks', type: [InfoBlockDto] })
  info_blocks: InfoBlockDto[];
}

export class UpdateStorefrontVersionDto {
  @ApiProperty({ enum: STOREFRONT_VERSION })
  @IsEnum(STOREFRONT_VERSION)
  @IsNotEmpty()
  version: STOREFRONT_VERSION;
}

export class UpdateStoreStockThresholdDto {
  @ApiProperty({ description: 'Global stock threshold for all items in the store', minimum: 0 })
  @IsNumber()
  @IsOptional()
  @Min(0)
  global_stock_threshold?: number;
}

export class LowStockItem {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  quantity: number;

  @ApiProperty()
  stock_threshold: number;

  @ApiProperty({ required: false })
  variant_id?: string;

  @ApiProperty({ required: false })
  variant_name?: string;

  @ApiProperty()
  is_out_of_stock: boolean;
}

export class PendingActionsResponseDto {
  @ApiProperty({ type: [Item], description: 'Items that are completely out of stock' })
  items_out_of_stock: any[];

  @ApiProperty({ type: [Order], description: 'Orders that are marked as unpaid' })
  orders_unpaid: any[];

  @ApiProperty({ type: [Order], description: 'Orders that are currently processing' })
  orders_processing: any[];

  @ApiProperty({ type: [LowStockItem], description: 'Items that have crossed stock thresholds' })
  low_stock_items: LowStockItem[];

  @ApiProperty({ type: [Order], description: 'Orders that are pending' })
  orders_pending: any[];

  @ApiProperty({ description: 'Summary counts for quick overview' })
  summary: {
    total_out_of_stock: number;
    total_unpaid_orders: number;
    total_processing_orders: number;
    total_low_stock_items: number;
    total_pending_orders: number;
  };
}
