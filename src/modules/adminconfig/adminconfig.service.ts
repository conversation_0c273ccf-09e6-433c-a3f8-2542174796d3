import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AdminConfig, AdminConfigDocument, ADMIN_CONFIG, FeatureFlags } from './adminconfig.schema';
import { FilterQuery, Model } from 'mongoose';

@Injectable()
export class AdminConfigService {
  constructor(
    @InjectModel(AdminConfig.name)
    private adminConfigModel: Model<AdminConfigDocument>,
  ) {}

  async getConfig(name: string): Promise<AdminConfig> {
    return this.adminConfigModel.findOne({ key: name });
  }

  async getConfigs(): Promise<AdminConfig[]> {
    return this.adminConfigModel.find();
  }

  async setConfig(config: Partial<AdminConfig>) {
    return (await this.adminConfigModel.findOne({ enabled: true })).updateOne(config);
  }

  async addConfg(name: string, value: string) {
    return this.adminConfigModel.create({
      key: name,
      value,
    });
  }

  async deleteConfig(config: FilterQuery<AdminConfig>) {
    return this.adminConfigModel.deleteOne(config);
  }

  async getAppConfiguration() {
    const [featureFlagsConfig, minimumVersionConfig] = await Promise.all([
      this.getConfig(ADMIN_CONFIG.FEATURE_FLAGS),
      this.getConfig(ADMIN_CONFIG.MINIMUM_APP_VERSION),
    ]);

    const featureFlags: FeatureFlags = featureFlagsConfig
      ? JSON.parse(featureFlagsConfig.value)
      : {
          subscriptions: true,
          deliveries: false,
          welcome_back_promo: false,
          store_reviews: false,
        };

    return {
      featureFlags,
      minimumRequiredAppVersion: minimumVersionConfig?.value || '1.0.0',
    };
  }

  async updateFeatureFlags(featureFlags: FeatureFlags) {
    const existingConfig = await this.getConfig(ADMIN_CONFIG.FEATURE_FLAGS);
    if (existingConfig) {
      return this.adminConfigModel.updateOne(
        { key: ADMIN_CONFIG.FEATURE_FLAGS },
        { value: JSON.stringify(featureFlags) },
      );
    }
    return this.addConfg(ADMIN_CONFIG.FEATURE_FLAGS, JSON.stringify(featureFlags));
  }

  async updateMinimumAppVersion(version: string) {
    const existingConfig = await this.getConfig(ADMIN_CONFIG.MINIMUM_APP_VERSION);
    if (existingConfig) {
      return this.adminConfigModel.updateOne({ key: ADMIN_CONFIG.MINIMUM_APP_VERSION }, { value: version });
    }
    return this.addConfg(ADMIN_CONFIG.MINIMUM_APP_VERSION, version);
  }
}
