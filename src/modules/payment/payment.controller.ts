import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Redirect,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { PaymentService } from './services/index.service';
import {
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiHeader,
  ApiOkResponse,
  ApiQuery,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { IRequest } from '../../interfaces/request.interface';
import {
  CreatePaymentDto,
  GetPaymentAnalyticsDto,
  GetInAppPaymentMethodsDto,
  GetPaymentMethodsDto,
  InitiatePaymentDto,
  InitiatePublicPaymentDto,
  PaymentHistoryQueryDto,
  InitiateZeepayWalletDebitDto,
  CreateTestPaymentDto,
} from './payment.dto';
import { Payment } from './payment.schema';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { PaystackConfig } from '../../config/types/paystack.config';
import { ConfigService } from '@nestjs/config';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { registerErrors } from '../../utils/errors.util';
import { PAYMENT_METHODS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../enums/payment.enum';
import { CountryPermissons, RolePermissions } from '../../decorators/permission.decorator';
import { SCOPES } from '../../utils/permissions.util';
import { RoleGuard } from '../../guards/role.guard';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { BrokerTransportService } from '../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Store } from '../store/store.schema';
import { PaymentMigrationsService } from './services/payment.migrations.service';
import { PaymentGettersService } from './services/payment.getters.service';
import { CountryGuard } from '../../guards/country.guard';
import { paymentsEnabledGuard } from '../../utils';
import { PaymentWebhooksService } from './services/payment.webhooks.service';
import { PaymentSubscriptionService } from './services/payment.subscription.service';
import { PaymentSubscriptionJob } from './services/payment-subscription-job.service';

@ApiTags('Payments')
@ApiExtraModels(Payment)
@Controller('payments')
export class PaymentController {
  private readonly paystackConfig: PaystackConfig;

  constructor(
    private readonly paymentService: PaymentService,
    private readonly paymentGettersService: PaymentGettersService,
    private readonly paymentMigrationsService: PaymentMigrationsService,
    private readonly paymentWebhooksService: PaymentWebhooksService,
    private readonly paymentSubscriptionJobService: PaymentSubscriptionJob,
    protected readonly brokerTransport: BrokerTransportService,
    configService: ConfigService,
    private readonly logger: Logger,
  ) {
    this.logger.setContext('payment.controller.ts');
    this.paystackConfig = configService.get<PaystackConfig>('paystackConfiguration');
  }

  @Post('')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @HttpCode(HttpStatus.CREATED)
  @ApiSecurity('bearer')
  @ApiCreatedResponse({ type: Payment })
  async createPayment(@Req() req: IRequest, @Body() body: CreatePaymentDto) {
    this.logger.log(req.user);
    const data = await this.paymentService.createPayment(
      {
        type: PAYMENT_TYPES.SUBSCRIPTION,
        payment_methods: body.payment_method_type,
        plan: body.plan,
        subscription: req.user.subscription.id,
        prefers_websockets: body.prefers_websockets,
      },
      req.user?.store?.id,
    );
    return {
      data,
    };
  }

  @Post('/test-payment')
  @RolePermissions(SCOPES.WALLETS.VIEW_WALLET)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  async initiateTestPayment(@Req() req: IRequest, @Body() body: CreateTestPaymentDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.paymentService.createTestPayment(req.user.store.id, body);

    return {
      message: 'Test payment initiated successfully',
      data,
    };
  }

  @Get('')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: [Payment] })
  async getPayments(@Req() req: IRequest) {
    const payments = await this.paymentGettersService.getAllPayments({
      owner: req.user.id,
    });

    return {
      message: 'Payments fetched successfully',
      data: payments,
    };
  }

  @Get('/subscriptions')
  @RolePermissions(SCOPES.SUBSCRIPTIONS.MANAGE_SUBSCRIPTIONS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: [Payment] })
  async getSubscriptionPayments(@Req() req: IRequest) {
    const payments = await this.paymentGettersService.getAllPayments({
      owner: req.user.id,
      type: PAYMENT_TYPES.SUBSCRIPTION,
    });

    return {
      message: 'Payments fetched successfully',
      data: payments,
    };
  }

  @Get('/payment-methods')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async getPaymentMethods(@Req() req: IRequest, @Query() query: GetInAppPaymentMethodsDto) {
    const data = await this.paymentGettersService.getPaymentMethods(query, req?.user?.store?.id);

    return {
      message: 'Payment methods fetched successfully',
      data,
    };
  }

  @Put(':reference/link-customer/:customer_id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async linkPaymentToCustomer(
    @Req() req: IRequest,
    @Param('customer_id') customerId: string,
    @Param('reference') reference: string,
  ) {
    const data = await this.paymentGettersService.linkCustomerToPayment(req?.user?.store?.id, customerId, reference);
    return {
      message: 'Payment customer updated successfully',
      data,
    };
  }

  @Put(':reference/link-invoice/:invoice_id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async linkInvoiceToPayment(
    @Req() req: IRequest,
    @Param('invoice_id') invoiceId: string,
    @Param('reference') reference: string,
  ) {
    const data = await this.paymentGettersService.linkInvoiceToPayment(req?.user?.store?.id, invoiceId, reference);
    return {
      message: 'Payment linked to invoice/order successfully',
      data,
    };
  }

  @Get('/analytics')
  @RolePermissions(SCOPES.INVOICES.VIEW_ANALYTICS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  @ApiSecurity('bearer')
  async getAnalytics(@Req() req: IRequest, @Query() query: GetPaymentAnalyticsDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.paymentGettersService.getAnalytics(req.user.store.id, query);

    return {
      message: 'Payment analytics fetched successfully',
      data,
    };
  }

  @Get('/analytics/by-method')
  @RolePermissions(SCOPES.INVOICES.VIEW_ANALYTICS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  @ApiSecurity('bearer')
  async getPaymentsByMethod(@Req() req: IRequest, @Query() query: GetPaymentAnalyticsDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.paymentGettersService.getTotalPaymentsByMethod(req.user.store.id, query);

    return {
      message: 'Payment methods analytics fetched successfully',
      data,
    };
  }

  @Get('/history')
  @RolePermissions(SCOPES.INVOICES.VIEW_ANALYTICS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  @ApiSecurity('bearer')
  async getPaymentHistory(@Req() req: IRequest, @Query() query: PaymentHistoryQueryDto) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.paymentGettersService.getPaymentHistory(req.user.store.id, query);

    return {
      message: 'Payment history fetched successfully',
      ...data,
    };
  }

  @Get('/recent-payments')
  @RolePermissions(SCOPES.INVOICES.VIEW_ANALYTICS)
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_COLLECT_PAYMENTS)
  @UseGuards(JwtAuthGuard, RoleGuard, CountryGuard)
  @ApiSecurity('bearer')
  async getRecentPayments(@Req() req: IRequest) {
    paymentsEnabledGuard(req.user.store as Store);
    const data = await this.paymentGettersService.getRecentPayments(req.user.store.id, {
      status: PAYMENT_STATUS.SUCCESS,
      'meta.invoice': { $exists: false },
    });

    return {
      message: 'Payment history fetched successfully',
      data,
    };
  }

  @Post('public/payment-methods')
  async getPublicPaymentMethods(@Body() body: GetPaymentMethodsDto) {
    const data = await this.paymentGettersService.getStorePublicPaymentMethods(body);

    return {
      message: 'Invoice payment methods fetched successfully',
      data,
    };
  }

  @Post('initiate')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async initiatePayment(@Req() req: IRequest, @Body() body: InitiatePaymentDto) {
    const payment = await this.paymentService.createPayment(body, req.user?.store?.id);

    return {
      message: 'Payment initiated successfully',
      data: payment,
    };
  }

  @Post('initiate/public')
  async intitiatePublicPayment(@Body() body: InitiatePublicPaymentDto) {
    const payment = await this.paymentService.createPublicPayment(body);

    return {
      message: 'Payment initiated successfully',
      data: payment,
    };
  }

  @Get('verify/:reference')
  async verifyPayment(@Param('reference') ref: string) {
    await this.paymentGettersService.verifyPayment(ref);
    return {
      message: 'Payment successful',
    };
  }

  @Get('status/:reference')
  async getPaymentStatus(@Param('reference') ref: string) {
    const data = await this.paymentGettersService.verifyPaymentsV2(ref);

    return {
      message: 'Payment status fetched successfully',
      data,
    };
  }

  @Get('verify-and-credit/:reference')
  async verifyAndCreditPaystackPayment(@Param('reference') ref: string) {
    await this.paymentGettersService.verifyAndCreditPaystackPayment(ref);
    return {
      message: 'Payment successful',
    };
  }

  @Get('verify-and-credit/payaza/:reference')
  async verifyAndCreditPayazaPayment(@Param('reference') ref: string) {
    await this.paymentGettersService.verifyAndCreditPayazaPayment(ref);
    return {
      message: 'Payment successful',
    };
  }

  @Put(':reference/cancel')
  async cancelPayment(@Param('reference') reference: string) {
    await this.paymentGettersService.cancelPayment(reference);
    return {
      message: 'Payment has been cancelled',
    };
  }

  @Get('/check-user-cards')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async checkForUserCards(@Req() req: IRequest) {
    return this.paymentGettersService.checkUserCards(req.user.id);
  }

  @Post('paystack-webhook')
  @Redirect('/webhooks/paystack')
  async paystackWebhook() {
    return {};
  }

  @Post('zeepay-debit-wallet')
  async debitWallet(@Body() request: InitiateZeepayWalletDebitDto) {
    const data = await this.paymentService.ZeepayWalletDebitPayment(request);

    return {
      message: 'payment initiated successfully',
      data,
    };
  }

  @Post('/migrate-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migratePayments() {
    return await this.paymentMigrationsService.migratePayments();
  }

  @Post('/add-currency-to-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addCurrencyToPayments() {
    return await this.paymentMigrationsService.addCurrencyToPayments();
  }

  @Post('/add-plan-option-to-payments')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async addPlanOptionToPayment() {
    return await this.paymentMigrationsService.addPlanOptionToPayments();
  }

  @Post('migrate-customers')
  @ApiQuery({ name: 'batchSize', required: false, type: Number })
  @ApiQuery({ name: 'delayMs', required: false, type: Number })
  async migrateReceiverDetails(
    @Query('batchSize') batchSize: number = 2000,
    @Query('delayMs') delayMs: number = 5000,
  ): Promise<{ updatedCount: number }> {
    return this.paymentMigrationsService.migratePaymentReceiverDetails(batchSize, delayMs);
  }

  @Get('/get-ref')
  async getRef() {
    return { ref: this.paymentService.paymentUtilsService.genRef() };
  }

  @Post('migrate-squad-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateTransferTxToPayment() {
    return await this.brokerTransport.send(BROKER_PATTERNS.WALLET.MIGRATE_SQUAD_PAYMENTS, {}).toPromise();
    // return this.walletService.migrateAllSquadTransactionsToPayments()
  }

  @Post('add-fees-to-trasfers')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addTransferGatewayFees() {
    return await this.brokerTransport.send(BROKER_PATTERNS.WALLET.ADD_TRANSFER_FEES, {}).toPromise();
    // return this.walletService.migrateAllSquadTransactionsToPayments()
  }

  @Post('/migrate-add-providers-to-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async addProvidersToPayments() {
    return await this.paymentMigrationsService.addProviderToPayments();
  }

  @Post('/migrate-upfront-payment-subscriptions')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateUpfrontPaymentSubscriptions() {
    return await this.paymentMigrationsService.migrateUpfrontPaymentSubscriptions();
  }

  @Post('/get-invoices-from-payment-references')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async getInvoicesFromPaymentReferences(@Body() body: { references: string[] }) {
    return await this.paymentService.getInvoicesFromPaymentReferences(body.references);
  }

  @Post('/revert-invoice-payments')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async revertInvoicePayments(@Body() body: { references: string[] }) {
    return await this.paymentService.revertInvoicePayments(body.references);
  }

  // @Post('/save-dd-auth')
  // @ApiExcludeEndpoint()
  // async saveDDAuth(@Body() data: any) {
  //   return await this.paymentWebhooksService.savePaystackDDAuth(data);
  // }

  // @Post('/charge-dd-auth')
  // @ApiExcludeEndpoint()
  // async chargeDDAuth() {
  //   return await this.paymentSubscriptionJobService.testCharge();
  // }

  // @Post('/test')
  // async testStuff() {
  //   const data = await this.userMessaging.sendSubscriptionExpiryMessage(formatPhoneNumber('+234-8052291107'), {
  //     customerName: 'Silas',
  //     planName: 'Basic',
  //     type: 'renewal',
  //   });

  //   return {
  //     data,
  //   };
  // }
}
