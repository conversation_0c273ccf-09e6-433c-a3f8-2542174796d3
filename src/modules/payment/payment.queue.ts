import { OnQueueActive, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { PaymentService } from './services/index.service';
import { Job } from 'bull';
import { getUserFriendlyPaymentMethod, toCurrency } from '../../utils';
import { getProductItemThumbnail } from '../../utils/functions';
import { PaymentDocument } from './payment.schema';
import { PAYMENT_METHODS, PAYMENT_PROVIDERS, PAYMENT_STATUS, PAYMENT_TYPES } from '../../enums/payment.enum';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { Invoice, InvoiceDocument } from '../invoice/invoice.schema';
import { TRANSACTION_CHANNELS, TransactionDocument, Wallet } from '../wallets/wallet.schema';
import { User } from '@sentry/node';
import { Store, StoreDocument } from '../store/store.schema';
import { chowbotMerchantFee, inwardZillaFee, walletFeeCalculator } from '../../utils/fees';
import { DELIVERY_METHODS, ORDER_CHANNELS, Order } from '../orders/order.schema';
import { deepMerge, getDocId, toKobo, toNaira } from '../../utils/functions';
import { Subscription } from '../subscription/subscription.schema';
import { Coupon } from '../item/discount-coupons/discounts-coupons.schema';
import { FEE_TYPES } from '../../enums/order.enum';
import { NOTIFICATION_TYPE } from '../user/notifications/notification.schema';
import { FREE_PAYMENT_THRESHOLD } from '../../utils/constants';

export type PaymentQueueJob<T = any> = {
  type: string;
  payload: T;
};

export type PublicPaymentJob = {
  reference: string;
  settlement: { amount_settled: number; fee: number };
  body: any;
  payment?: PaymentDocument;
  meta?: any;
};

export type DelayedDeliveryJob = {
  payment: PaymentDocument;
};

@Processor(QUEUES.PAYMENT)
export class PaymentQueue {
  constructor(private paymentService: PaymentService) {}

  @OnQueueActive()
  onActive(job: Job<PaymentQueueJob>) {
    console.log(`Processing job ${job.id} of type ${job.name} with data ${JSON.stringify(job.data)}...`);
  }

  @OnQueueFailed() onFailed(job: Job, error: Error) {
    console.log(`Failed to process job ${job.id} of type ${job.name} with data, error: ${error.message}`);
  }

  @OnQueueCompleted()
  onCompleted(job: Job, result: any) {
    console.log(`Job ${job.id} of type ${job.name} successfully completed, result: ${result}`);
    // job.remove();
  }

  @Process({ name: QUEUES.PAYMENT, concurrency: 1 })
  async handlePaymentJobs(job: Job<PaymentQueueJob>) {
    switch (job.data.type) {
      case JOBS.PUBLIC_PAYMENT:
        this.publicPayment(job);
        break;
      case JOBS.DELAYED_DELIVERY:
        this.initiateDelayedDelivery(job);
        break;
      default:
      // do nothing
    }
  }

  async publicPayment(job: Job<PaymentQueueJob<PublicPaymentJob>>) {
    let { reference, body, settlement, meta } = job.data.payload;

    let invoice: Invoice;
    let order: Order;
    let isChowbotPayment = false;

    const payment = await this.paymentService.paymentModel.findOne({
      reference: reference,
    });

    if (payment && Math.round(payment.amount) > Math.round(settlement.amount_settled + settlement.fee)) {
      console.log('FRAUDULENT PAYMENT');
      return; // The amount you paid is not up to the amount you were supposed to pay, this should never happpen but crafty people exist.
    }

    if (!payment) {
      this.paymentService.logger.log(`payment with reference ${reference}, does not exist`);
      job.remove().catch(console.log);
      return;
    }

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.paymentService.logger.log(`this payment is already successful, payment: ${payment}, data: ${body}`);
      job.remove().catch(console.log);
      return;
    }

    if (payment.type === PAYMENT_TYPES.INVOICE) {
      invoice = await this.paymentService.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
          invoice_id: payment?.meta?.invoice,
        })
        .toPromise();

      if (!invoice) {
        this.paymentService.logger.log(`invoice with id ${payment?.meta?.invoice}, does not exist`.toUpperCase());
        job.remove().catch(console.log);
        return;
      }
    }

    const store = await this.paymentService.brokerTransport
      .send<StoreDocument>(BROKER_PATTERNS.STORE.GET_STORE, { _id: invoice?.store ? invoice?.store : payment?.store })
      .toPromise();

    if (!store) {
      this.paymentService.logger.log(`Store not found for payment: ${payment?.reference}`.toUpperCase());
      job.remove().catch(console.log);
      return;
    }

    const owner = await this.paymentService.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: store?.owner })
      .toPromise();

    if (invoice && invoice.order) {
      order = await this.paymentService.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN, {
          _id: invoice.order,
        })
        .toPromise();

      isChowbotPayment = order.channel === ORDER_CHANNELS.CHATBOT && !!order?.meta?.chowbot?.session_id;
    }

    if (isChowbotPayment) {
      const orderFees = order.fees.reduce((cumm, fee) => cumm + Math.abs(fee.amount), 0);
      const amountForFoodBought = order.total_amount - orderFees;

      const subscription = store?.subscription as Subscription;

      const merchantFees = chowbotMerchantFee(toKobo(amountForFoodBought), subscription?.meta?.chowbot_fee);
      payment.merchant_fees = merchantFees;
    } else {
      const currentPlan = store?.current_plan?.plan_type;

      const isBaseWalletPayment = payment.currency === store?.currencies?.default;
      payment.merchant_fees = walletFeeCalculator(payment.amount, payment.currency, currentPlan);

      // if the payment is for the base wallet, check if the wallet has crossed 10 payments
      // if it hasn't, set the fee to 0
      if (isBaseWalletPayment) {
        const baseWallet = await this.paymentService.brokerTransport
          .send<Wallet>(BROKER_PATTERNS.WALLET.GET_WALLET, {
            storeId: getDocId(store),
          })
          .toPromise();

        if (!baseWallet || !baseWallet?.meta?.has_crossed_free_threshold) {
          const paymentCount = await this.paymentService.brokerTransport
            .send<number>(BROKER_PATTERNS.WALLET.COUNT_CREDIT_PAYMENTS, {
              walletId: getDocId(baseWallet),
            })
            .toPromise();

          if (paymentCount <= FREE_PAYMENT_THRESHOLD) {
            payment.merchant_fees = 0;
          }
        }
      }
    }

    if (payment.type === PAYMENT_TYPES.TEST) {
      payment.merchant_fees = 0;
    }

    payment.status = PAYMENT_STATUS.SUCCESS;
    payment.gateway_amount_settled = settlement.amount_settled;
    payment.gateway_charge = settlement.fee;
    payment.meta = deepMerge(payment.meta, meta);

    payment.markModified('meta');
    await payment.save();

    const actionData = {
      [PAYMENT_TYPES.TEST]: {
        channel: TRANSACTION_CHANNELS.TEST_PAYMENT,
        narration: `Test payment of ${toCurrency((payment.amount / 100).toString(), payment?.currency)}`,
        source: {
          name: owner?.name,
          method: getUserFriendlyPaymentMethod(payment.payment_method),
          purpose: `Test Payment`,
        },
        customer: `from ${owner?.name}`,
        order_or_invoice: `for Test Payment`,
      },
      [PAYMENT_TYPES.INVOICE]: {
        channel: TRANSACTION_CHANNELS.INVOICE_PAYMENT,
        narration: invoice?.title,
        source: {
          name: invoice?.receiver?.name,
          purpose: `Payment for ${invoice?.order ? `Order ${invoice?.order}` : `Invoice ${invoice?.invoice_id}`}`,
          method: getUserFriendlyPaymentMethod(payment.payment_method),
        },
        customer: invoice?.receiver.name.trim() ? 'from ' + invoice?.receiver.name : '',
        order_or_invoice: invoice?.order ? `for order ${invoice?.order}` : `for invoice ${invoice?.invoice_id}`,
      },
    };

    const { channel, narration, source, customer, order_or_invoice } = actionData[payment.type];

    const walletId = store.wallets.find((wallet) => wallet.currency === payment.currency)?.id;
    // 0% fee, add fee to the transaction & 2.5% for zilla payments - FIXES
    const wallet = await this.paymentService.brokerTransport
      .send<{ wallet: Wallet; transaction: TransactionDocument }>(BROKER_PATTERNS.WALLET.CREDIT, {
        walletId: walletId,
        amount: payment.amount,
        channel,
        narration,
        fee: payment.merchant_fees,
        meta: {
          payment_id: payment._id,
        },
        source,
      })
      .toPromise();

    if (payment.type === PAYMENT_TYPES.INVOICE && invoice) {
      const updatedInvoice = await this.paymentService.brokerTransport
        .send<Invoice>(BROKER_PATTERNS.INVOICE.INVOICE_PAID, {
          id: invoice?.invoice_id,
          payment_method: payment.payment_method,
          paymentId: getDocId(payment),
        })
        .toPromise();

      this.paymentService.brokerTransport.emit(BROKER_PATTERNS.SSE.TARGETTED_MESSAGE, {
        id: 'PAYMENTS.' + invoice?.invoice_id,
        data: {
          event: 'PAYMENT_SUCCESSFUL',
          payload: { payment, receipt: updatedInvoice?.receipt, invoice: invoice?.invoice_id },
        },
      });

      // this.sseService.sendToClient('PAYMENTS.' + invoice?.invoice_id, {
      //   event: 'PAYMENT_SUCCESSFUL',
      //   payload: { payment, receipt: updatedInvoice?.receipt, invoice: invoice?.invoice_id },
      // });

      if (order) {
        const coupon = order?.coupon_code
          ? await this.paymentService.brokerTransport
              .send<Coupon>(BROKER_PATTERNS.ITEM.GET_COUPON, {
                couponCode: order?.coupon_code,
                user: { store: { id: getDocId(store) } },
              })
              .toPromise()
          : null;

        if (coupon && coupon?.sponsored_by_catlog) {
          const couponAmount = Math.abs(order.fees.find((fee) => fee.type === FEE_TYPES.COUPON)?.amount);

          if (couponAmount > 0) {
            await this.paymentService.brokerTransport
              .send<any>(BROKER_PATTERNS.WALLET.CREDIT, {
                walletId: walletId,
                amount: toKobo(couponAmount), //@silas convert currencies here if we want to support multiple currencies
                channel: TRANSACTION_CHANNELS.COUPON_REFUND,
                narration: `Credit for coupon ${coupon.coupon_code} usage on order: ${getDocId(order)}`,
                fee: 0,
                meta: { order: getDocId(order) },
                source: {
                  name: 'Catlog',
                  purpose: `Coupon Credit`,
                  method: '',
                },
              })
              .toPromise();
          }
        }

        if (!store.onboarding_steps?.has_taken_first_order_with_payment) {
          await this.paymentService.brokerTransport
            .send(BROKER_PATTERNS.STORE.UPDATE_FIRST_ORDER_WITH_PAYMENT, getDocId(store))
            .toPromise();
        }
      }

      if (isChowbotPayment) {
        //get updated order with receipt
        order = await this.paymentService.brokerTransport
          .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN, {
            _id: invoice.order,
          })
          .toPromise();

        //Todo: probably run other checks here
        await this.paymentService.brokerTransport
          .send<Invoice>(BROKER_PATTERNS.WHATSAPP_BOT.HANDLE_SUCESSFUL_PAYMENT, {
            sessionId: order?.meta?.chowbot?.session_id,
            paymentReference: payment?.reference,
            receiptId: order?.receipt,
          })
          .toPromise();

        if (store.third_party_configs?.chowdeck?.auto_delivery && order?.delivery) {
          await this.paymentService.createPayment(
            {
              type: PAYMENT_TYPES.DELIVERY,
              payment_methods: [PAYMENT_METHODS.WALLET],
              currency: order?.currency,
              delivery: getDocId(order?.delivery),
            },
            store.id,
          );
        }
      }
    }

    if (wallet.wallet?.auto_send_funds_to) {
      await this.paymentService.brokerTransport
        .send<Wallet>(BROKER_PATTERNS.WALLET.AUTO_SEND_FUNDS_TO_ACCOUNT, getDocId(wallet.transaction))
        .toPromise();
    }

    //charge transfer fee on merchant if the payment is a transfer and the amount paid is less than the amount expected
    if (
      payment.type === PAYMENT_TYPES.INVOICE &&
      payment.payment_method === PAYMENT_METHODS.TRANSFER &&
      (!!store.configuration?.pass_transfer_fee_to_merchant || payment.meta?.pass_fee_to_merchant)
    ) {
      await this.paymentService.brokerTransport
        .send<Wallet>(BROKER_PATTERNS.WALLET.CHARGE_CUSTOMER_TRANSFER_FEE, {
          walletId: walletId,
          amount: payment.amount_with_charge - payment.amount,
          narration: `Customer transfer fee for ${
            invoice?.order ? `order ${invoice?.order}` : `invoice ${invoice?.invoice_id}`
          }`,
          paymentId: getDocId(payment),
        })
        .toPromise();
    }

    //Send Notifications
    const paymentReceivedData = {
      name: owner.name.split(' ')[0],
      received: toCurrency((payment.amount / 100).toString(), payment?.currency),
      customer,
      order_or_invoice,
      current_balance: toCurrency((wallet?.wallet?.balance / 100).toString(), payment?.currency),
      fee: toCurrency(String(payment.merchant_fees / 100), payment?.currency),
      credited: toCurrency(((payment.amount - payment.merchant_fees) / 100).toString(), payment?.currency),
      date: new Date().toDateString(),
      funds_link: process.env.CATLOG_APP,
      payment_method_icon: paymentImagesMap[payment.payment_method].icon,
      payment_method_name: paymentImagesMap[payment.payment_method].label,
    };

    this.paymentService.resend.sendEmail(BROKER_PATTERNS.MAIL.PAYMENT_RECEIVED, {
      to: owner.email,
      subject: `${source?.name} just paid you ${toCurrency((payment.amount / 100).toString(), payment?.currency)} 💰`,
      data: paymentReceivedData,
    });

    if (payment.type === PAYMENT_TYPES.INVOICE) {
      await this.paymentService.brokerTransport
        .emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
          message: {
            title: invoice?.order
              ? `Payment for order ${invoice?.order} 💰`
              : `Payment for invoice ${invoice?.invoice_id} 💰`,
            message: `${invoice?.receiver.name} paid you ${toCurrency(
              (payment.amount / 100).toString(),
              payment?.currency,
            )} via ${paymentImagesMap[payment.payment_method].label}`,
            path: invoice?.order
              ? `/orders/${invoice?.order}`
              : `/invoices?search=${invoice?.invoice_id}&showInvoice=true`,
          },
          owner_only: false,
          store: store?.id,
          notification_type: invoice?.order
            ? NOTIFICATION_TYPE.PAYMENT_RECEIVED_ORDER
            : NOTIFICATION_TYPE.PAYMENT_RECEIVED_INVOICE,
          data: {
            id: payment?._id,
            store_id: store?.id,
            source_amount: toCurrency(toNaira(payment.amount), payment?.currency),
            destination_currency: payment?.currency,
          },
        })
        .toPromise();
    }

    if (payment.type === PAYMENT_TYPES.INVOICE && invoice?.receiver?.email) {
      if (invoice.order) {
        this.paymentService.resend.sendEmail(BROKER_PATTERNS.MAIL.ORDER_RECEIVED_CUSTOMER, {
          to: invoice.receiver.email,
          subject: `Your order was successful 🤝🏾`,
          data: {
            name: invoice?.receiver.name.split(' ')[0],
            preview_text: `Your payment for order ${invoice?.order} was successful`,
            store_name: store.name,
            store_color: store?.configuration.color,
            store_logo: store?.logo,
            order: {
              order_id: invoice.order,
              link: `${process.env.CATLOG_WWW}/orders/${invoice.order}?forCustomer=true`,
              order_status: order.status,
              products: order.items.map(({ snapshot: item, quantity, variant }) => ({
                image: getProductItemThumbnail(item),
                name: item?.name,
                price: variant ? variant.price : item.price,
                quantity: quantity.toLocaleString(),
              })),
              total: order.total_amount,
              fees: order.fees.map((fee) => ({
                label: fee.label,
                amount: fee.amount,
              })),
              delivery_method: order.delivery_method,
              delivery_area: order?.delivery_info?.delivery_area?.name,
              delivery_address: order?.delivery_info?.delivery_address,
              currency: order.currency,
            },
          },
        });
      } else {
        this.paymentService.resend.sendEmail(BROKER_PATTERNS.MAIL.PAYMENT_SUCCESSFUL, {
          to: invoice.receiver.email,
          subject: `Your payment of ${toCurrency(
            (payment.amount / 100).toString(),
            payment?.currency,
          )} was successful 🤝🏾`,
          data: {
            name: invoice?.receiver.name.split(' ')[0],
            amount: toCurrency((payment.amount / 100).toString(), payment?.currency),
            store_name: store.name,
            order_or_invoice: invoice?.order ? `order ${invoice?.order}` : `invoice ${invoice?.invoice_id}`,
            reference: payment.reference,
            date: new Date().toDateString(),
            payment_method_icon: paymentImagesMap[payment.payment_method].icon,
            payment_method_name: paymentImagesMap[payment.payment_method].label,
          },
        });
      }
    }

    if (payment.type === PAYMENT_TYPES.TEST) {
      await this.paymentService.brokerTransport
        .send<void>(BROKER_PATTERNS.STORE.UPDATE_STORE, {
          filter: { _id: store?.id },
          update: {
            onboarding_steps: {
              ...store.onboarding_steps,
              test_payment_made: true,
            },
          },
        })
        .toPromise();

      this.paymentService.brokerTransport.emit(BROKER_PATTERNS.SSE.TARGETTED_MESSAGE, {
        id: 'TEST_PAYMENT.' + store.id,
        data: {
          event: 'TEST_PAYMENT_SUCCESSFUL',
          payload: payment,
          reference: payment.reference,
          balance: wallet?.wallet?.balance,
        },
      });

      // this.sseService.sendToClient('TEST_PAYMENT.' + store.id, {
      //   event: 'TEST_PAYMENT_SUCCESSFUL',
      //   payload: payment,
      // });
    }

    job.remove().catch(console.log);
    return;
  }

  async initiateDelayedDelivery(job: Job<PaymentQueueJob<DelayedDeliveryJob>>) {
    const { payment } = job.data.payload;

    await this.paymentService.brokerTransport
      .send(BROKER_PATTERNS.DELVERIES.INITIATE_DELIVERY, {
        deliveryId: payment.meta?.delivery,
        paymentRef: payment?.reference,
      })
      .toPromise();

    // await this.sendNotifications(payment);
    await this.paymentService.paymentDeliveriesService.sendSSEEvent(payment);

    job.remove().catch(console.log);
    return;
  }
}

const paymentImagesMap = {
  [PAYMENT_METHODS.PAYSTACK]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1674921788/email-images/paystack.png',
    label: 'Card | Paystack',
  },
  [PAYMENT_METHODS.STRIPE]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1735994550/email-images/stripe.png',
    label: 'Stripe',
  },
  [PAYMENT_METHODS.LEATHERBACK]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1735994570/email-images/Leatherback.png',
    label: 'Leatherback',
  },
  [PAYMENT_METHODS.TRANSFER]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1674921788/email-images/transfer.png',
    label: 'Bank Transfer',
  },
  [PAYMENT_METHODS.ZILLA]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1674921788/email-images/zilla.png',
    label: 'Zilla',
  },
  [PAYMENT_METHODS.MONO_DIRECT_PAY]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1675382117/email-images/directpay.png',
    label: 'Mono DirectPay',
  },
  [PAYMENT_METHODS.THEPEER]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1680497607/email-images/thepeer.png',
    label: 'Thepeer',
  },
  [PAYMENT_METHODS.STARTBUTTON]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1674921788/email-images/paystack.png',
    label: 'Paystack',
  },
  [PAYMENT_METHODS.MOMO]: {
    icon: 'https://res.cloudinary.com/catlog/image/upload/v1731792887/email-images/mobile-money.png',
    label: 'Mobile Money',
  },
};
