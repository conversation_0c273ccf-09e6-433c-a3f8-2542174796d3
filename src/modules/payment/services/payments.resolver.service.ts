import { Injectable, Logger } from '@nestjs/common';
import { Payment, PaymentDocument } from '../payment.schema';
import { ResendRepository } from '../../../repositories/resend.repository';
import mongoose, { InjectConnection } from '@nestjs/mongoose';
import { ClientSession, Model } from 'mongoose';
import { PAYMENT_STATUS } from '../../../enums/payment.enum';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Card } from '../card.schema';
import { InjectModel } from '@nestjs/mongoose';
import { CardDocument } from '../card.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
@Injectable()
export class PaymentsResolverService {
  constructor(
    @InjectModel(Payment.name)
    public readonly paymentModel: Model<PaymentDocument>,
    @InjectModel(Card.name)
    protected readonly cardModel: Model<CardDocument>,
    public readonly brokerTransport: BrokerTransportService,
    public readonly logger: Logger, // public readonly sseService: SSEService,
    public readonly customerIo: CustomerIoRepository,
  ) {}

  async updateCreditPayment(payment: PaymentDocument, narration: string) {
    const partnerPayment = await this.paymentModel
      .findOneAndUpdate(
        {
          _id: payment.partner_payment,
        },
        { status: PAYMENT_STATUS.SUCCESS },
      )
      .exec();

    await this.brokerTransport
      .send(BROKER_PATTERNS.USER.CREDITS.RECORD_DEBIT, {
        user_id: partnerPayment.owner,
        amount: partnerPayment.amount_with_charge,
        meta: {
          payment_id: partnerPayment._id,
        },
        narration,
      })
      .toPromise();
  }

  async resolveAndUpdatePayment(
    payment: PaymentDocument,
    gatewayPaymentInfo: { amount: number; fees: number },
    session: ClientSession,
    meta: any = {},
  ) {
    const resolvedPayment = await this.paymentModel
      .findOneAndUpdate(
        {
          _id: payment.id,
        },
        {
          status: PAYMENT_STATUS.SUCCESS,
          gateway_amount_settled: Number(gatewayPaymentInfo.amount) - Number(gatewayPaymentInfo.fees),
          gateway_charge: Number(gatewayPaymentInfo.fees),
          meta: { ...payment.meta, ...meta },
        },
        { session },
      )
      .exec();

    return resolvedPayment;
  }

  async sendWebsocketEvent(payment: PaymentDocument) {
    this.brokerTransport.emit(BROKER_PATTERNS.WEBSOCKET.TARGETTED_MESSAGE, {
      id: 'PAYMENTS.' + payment.reference,
      data: {
        event: 'PAYMENT_SUCCESSFUL',
        payload: { payment, reference: payment.reference },
      },
    });
  }

  async sendSSEEvent(payment: PaymentDocument) {
    this.brokerTransport.emit(BROKER_PATTERNS.SSE.TARGETTED_MESSAGE, {
      id: 'PAYMENTS.' + payment.reference,
      data: {
        event: 'PAYMENT_SUCCESSFUL',
        payload: { payment, reference: payment.reference },
      },
    });
  }
}
