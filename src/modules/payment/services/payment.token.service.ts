import mongoose, { ClientSession, Model } from 'mongoose';
import { Payment, PaymentDocument } from '../payment.schema';
import { PaymentService } from './index.service';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Logger } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { PAYMENT_STATUS } from '../../../enums/payment.enum';
import { User } from '../../user/user.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { Card, CardDocument } from '../card.schema';
import { formatDate } from '../../../utils/time';
import { Subscription } from '../../subscription/subscription.schema';
import { toCurrency } from '../../../utils';
import { toNaira } from '../../../utils/functions';
import { ResendRepository } from '../../../repositories/resend.repository';
import { PaymentsResolverService } from './payments.resolver.service';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
export class PaymentTokenService extends PaymentsResolverService {
  constructor(
    protected readonly paymentService: PaymentService,
    @InjectModel(Payment.name)
    public readonly paymentModel: Model<PaymentDocument>,
    @InjectModel(Card.name)
    protected readonly cardModel: Model<CardDocument>,
    public readonly brokerTransport: BrokerTransportService,
    public readonly logger: Logger,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
    private readonly resend: ResendRepository, // public readonly sseService: SSEService,
    public readonly customerIo: CustomerIoRepository,
  ) {
    super(paymentModel, cardModel, brokerTransport, logger, customerIo);
  }

  async paystackWebhook(data: any, payment: PaymentDocument) {
    const { authorization } = data;

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    this.handleSuccessfulTokenPayments(payment, { amount: data.amount, fees: data.fees });
  }

  async handleSuccessfulTokenPayments(payment: PaymentDocument, gatewayPaymentInfo: { amount: number; fees: number }) {
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: payment.owner })
      .toPromise();

    const token_amount = payment.meta?.token_amount;

    if (!token_amount) {
      return;
    }

    try {
      const session = await this.connection.startSession();
      session.startTransaction();

      const resolvedPayment = await this.resolveAndUpdatePayment(payment, gatewayPaymentInfo, session);

      await session.commitTransaction(async () => {
        if (resolvedPayment?.partner_payment) {
          await this.updateCreditPayment(resolvedPayment, `Payment for ${token_amount} tokens`);
        }

        const subscription = await this.brokerTransport
          .send<Subscription>(BROKER_PATTERNS.PAYMENT.EXTRA_TOKEN_PURCHASE, {
            subscriptionId: payment.meta?.subscription,
            tokenAmount: token_amount,
            paymentId: payment.id,
            storeId: payment.meta.store,
          })
          .toPromise();

        await this.sendNotifications(payment, token_amount, user, subscription.meta?.chowbot_tokens?.tokens_balance);
      });
    } catch (error) {
      this.logger.log(`error occurred in processing payment ${error}`);
    }
  }

  async sendNotifications(payment: PaymentDocument, token_amount: number, user: User, tokens_balance: number) {
    if (payment?.meta?.prefers_websockets) {
      this.sendWebsocketEvent(payment);
    } else {
      this.sendSSEEvent(payment);
    }

    const emailData = {
      to: user.email,
      subject: 'Your token purchase was successful ✅',
      data: {
        name: user.name.split(' ')[0],
        payment_amount: toCurrency(toNaira(payment.amount), payment.currency),
        token_amount: token_amount.toString(),
        payment_method_name: payment.payment_method.split('_').join(' '),
        date: formatDate(new Date()),
        tokens_balance: tokens_balance.toString(),
        payment_reference: String(payment.reference),
        cta_link: `${process.env.CATLOG_DASHBOARD}/chowbot`,
      },
    };

    await this.resend.sendEmail(BROKER_PATTERNS.MAIL.EXTRA_TOKEN_PURCHASE, emailData);
  }
}
