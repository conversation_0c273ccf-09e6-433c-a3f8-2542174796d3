import mongoose, { ClientSession, Model } from 'mongoose';
import { Payment, PaymentDocument } from '../payment.schema';
import { PaymentService } from './index.service';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Logger } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { PAYMENT_STATUS } from '../../../enums/payment.enum';
import { User } from '../../user/user.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { Card, CardDocument } from '../card.schema';
import { formatDate } from '../../../utils/time';
import { Subscription } from '../../subscription/subscription.schema';
import { toCurrency } from '../../../utils';
import { toNaira } from '../../../utils/functions';
import { ResendRepository } from '../../../repositories/resend.repository';
import { PaymentsResolverService } from './payments.resolver.service';
import { DomainPurchase } from '../../store/domains/domain-purchase.schema';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
import { ******************** } from '../../../repositories/zeepay.repository';
import { zeepayActualCharge } from '../../../utils/fees';

export class PaymentDomainPurchaseService extends PaymentsResolverService {
  constructor(
    protected readonly paymentService: PaymentService,
    @InjectModel(Payment.name)
    public readonly paymentModel: Model<PaymentDocument>,
    @InjectModel(Card.name)
    protected readonly cardModel: Model<CardDocument>,
    public readonly brokerTransport: BrokerTransportService,
    public readonly logger: Logger,
    @InjectConnection()
    protected readonly connection: mongoose.Connection,
    private readonly resend: ResendRepository,
    public readonly customerIo: CustomerIoRepository,
  ) {
    super(paymentModel, cardModel, brokerTransport, logger, customerIo);
  }

  async paystackWebhook(data: any, payment: PaymentDocument) {
    const { authorization } = data;

    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    this.handleSuccessfulDomainPurchasePayments(payment, { amount: data.amount, fees: data.fees });
  }

  async zeepayWebhook(data: ********************, payment: PaymentDocument) {
    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    const zeepayFees = zeepayActualCharge(payment.amount);

    this.handleSuccessfulDomainPurchasePayments(
      payment,
      {
        amount: data?.amount_settled ?? payment.amount_with_charge - zeepayFees,
        fees: data?.fee_charged ?? zeepayFees,
      },
      { zeepay: { zeepay_id: data?.zeepay_id, gateway_id: data?.gateway_id } },
    );
  }

  async startbuttonWebhook(data: any, payment: PaymentDocument) {
    if (payment.status === PAYMENT_STATUS.SUCCESS) {
      this.logger.log(`this payment is already successful, payment: ${payment}, data: ${data}`);
      return;
    }

    this.handleSuccessfulDomainPurchasePayments(payment, { amount: Number(data.amount), fees: Number(data.feeAmount) });
  }

  async handleSuccessfulDomainPurchasePayments(
    payment: PaymentDocument,
    gatewayPaymentInfo: { amount: number; fees: number },
    meta: any = {},
  ) {
    const user = await this.brokerTransport
      .send<User>(BROKER_PATTERNS.USER.GET_USER, { _id: payment.owner })
      .toPromise();

    const domainPurchaseId = payment.meta?.domain_purchase;

    if (!domainPurchaseId) {
      return;
    }

    try {
      const session = await this.connection.startSession();
      session.startTransaction();

      const resolvedPayment = await this.resolveAndUpdatePayment(payment, gatewayPaymentInfo, session, meta);

      await session.commitTransaction(async () => {
        if (resolvedPayment?.partner_payment) {
          await this.updateCreditPayment(resolvedPayment, `Payment for domain: ${payment.meta?.domain}`);
        }

        await this.brokerTransport
          .send<DomainPurchase>(BROKER_PATTERNS.DOMAIN.COMPLETE_PURCHASE, {
            purchaseId: domainPurchaseId,
            paymentId: payment.id,
            nameservers: [],
          })
          .toPromise();

        if (payment?.meta?.prefers_websockets) {
          this.sendWebsocketEvent(payment);
        } else {
          this.sendSSEEvent(payment);
        }
      });
    } catch (error) {
      this.logger.log(`error occurred in processing payment ${error}`);
    }
  }
}
