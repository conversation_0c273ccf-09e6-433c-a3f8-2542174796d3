import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  Get,
  NotFoundException,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBody,
  ApiExcludeEndpoint,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { IRequest } from '../../../interfaces/request.interface';
import { DomainService } from './domain.service';
import { PAYMENT_METHODS } from '../../../enums/payment.enum';
import { InternalApiJWTGuard } from '../../../guards/api.guard';

@Controller('domains')
@ApiTags('Domains')
export class DomainController {
  constructor(private readonly domainService: DomainService) {}

  @Get('/check/:domain')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Check domain availability and pricing' })
  @ApiParam({
    name: 'domain',
    description: 'Domain name to check availability for',
    type: 'string',
    required: true,
  })
  @ApiResponse({ status: 200, description: 'Domain availability check successful' })
  @ApiResponse({ status: 400, description: 'Invalid domain format' })
  async checkDomainAvailability(@Param('domain') domain: string, @Req() req: IRequest) {
    const data = await this.domainService.checkDomainAvailability(domain, req.user.store.country, req.user.store.id);
    return {
      message: 'Domain availability check successful',
      data,
    };
  }

  @Post('/purchase')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Initiate domain purchase process',
    description:
      'Starts the domain purchase process. The system will automatically populate contact information from store and owner details if not provided.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        domain: {
          type: 'string',
          example: 'example.com',
          description: 'The domain name to purchase',
        },
      },
      required: ['domain'],
    },
  })
  @ApiResponse({ status: 201, description: 'Domain purchase initiated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid domain or domain not available' })
  async initiateDomainPurchase(@Req() req: IRequest, @Body() body: { domain: string }) {
    const domainPurchase = await this.domainService.initiateDomainPurchase(req.user.store.id, body.domain);

    return {
      message: 'Domain purchase initiated successfully',
      data: domainPurchase,
    };
  }

  @Post('/purchase/:id/payment')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Create payment for a domain purchase' })
  @ApiParam({
    name: 'id',
    description: 'ID of the domain purchase',
    type: 'string',
    required: true,
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        payment_methods: {
          type: 'array',
          items: {
            type: 'string',
            enum: Object.values(PAYMENT_METHODS),
          },
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Payment created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid domain purchase ID' })
  @ApiResponse({ status: 403, description: 'Domain purchase does not belong to your store' })
  async createDomainPurchasePayment(
    @Req() req: IRequest,
    @Param('id') purchaseId: string,
    @Body() body: { payment_methods: PAYMENT_METHODS[] },
  ) {
    console.log('purchaseID', purchaseId);
    // Validate that the domain purchase belongs to the user's store
    const domainPurchases = await this.domainService.getDomainPurchases(req.user.store.id);
    const purchaseBelongsToStore = domainPurchases.some((purchase) => purchase._id.toString() === purchaseId);

    if (!purchaseBelongsToStore) {
      throw new ForbiddenException('This domain purchase does not belong to your store.');
    }

    const paymentResult = await this.domainService.createDomainPurchasePayment(
      purchaseId,
      body.payment_methods,
      req.user.store.id,
    );

    return {
      message: 'Payment created successfully',
      data: paymentResult,
    };
  }

  @Get('/purchases')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all domain purchases for a store' })
  @ApiResponse({ status: 200, description: 'Domain purchases fetched successfully' })
  async getDomainPurchases(@Req() req: IRequest) {
    const data = await this.domainService.getDomainPurchases(req.user.store.id);
    return {
      message: 'Domain purchases fetched successfully',
      data,
    };
  }

  @Get('/purchases/:id')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get a specific domain purchase' })
  @ApiParam({
    name: 'id',
    description: 'ID of the domain purchase',
    type: 'string',
    required: true,
  })
  @ApiResponse({ status: 200, description: 'Domain purchase fetched successfully' })
  @ApiResponse({ status: 404, description: 'Domain purchase not found' })
  @ApiResponse({ status: 403, description: 'Domain purchase does not belong to your store' })
  async getDomainPurchase(@Req() req: IRequest, @Param('id') purchaseId: string) {
    const domainPurchase = await this.domainService.getDomainPurchase(purchaseId);

    if (!domainPurchase) {
      throw new NotFoundException('Domain purchase not found');
    }

    if (domainPurchase.store.toString() !== req.user.store.id) {
      throw new ForbiddenException('This domain purchase does not belong to your store');
    }

    return {
      message: 'Domain purchase fetched successfully',
      data: domainPurchase,
    };
  }

  @Post('/purchases/:id/retry')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiOperation({ summary: 'Complete a domain purchase after payment' })
  @ApiParam({
    name: 'id',
    description: 'ID of the domain purchase',
    type: 'string',
    required: true,
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        paymentId: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'Domain purchase completed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid domain purchase ID or payment not successful' })
  @ApiResponse({ status: 403, description: 'Domain purchase does not belong to your store' })
  async completeDomainPurchase(@Req() req: IRequest, @Param('id') purchaseId: string) {
    const result = await this.domainService.completeDomainPurchase(purchaseId);

    return {
      message: 'Domain purchase completed successfully',
      data: result,
    };
  }

  // @Post('/test-go54-login')
  // @ApiSecurity('bearer')
  // @UseGuards(JwtAuthGuard)
  // @ApiOperation({ summary: 'Test Go54 login functionality' })
  // @ApiResponse({ status: 200, description: 'Login successful' })
  // @ApiResponse({ status: 400, description: 'Login failed' })
  // async testGo54Login() {
  //   // Call the login method on the Go54Repository
  //   const loginResult = await this.domainService.testGo54Login();

  //   if (loginResult.error) {
  //     throw new BadRequestException(`Login failed: ${loginResult.error}`);
  //   }

  //   return {
  //     message: 'Go54 login successful',
  //     data: loginResult.data,
  //   };
  // }
}
