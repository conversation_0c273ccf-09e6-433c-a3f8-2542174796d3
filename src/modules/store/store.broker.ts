import { Controller } from '@nestjs/common';
import { StoreService } from './services/store.service';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import mongoose, { FilterQuery, Model } from 'mongoose';
import { Store, StoreDocument } from './store.schema';
import { InjectModel } from '@nestjs/mongoose';
import { DeliveryArea, DeliveryAreaDocument } from './delivery-areas/delivery-areas.schema';
import { DeliveryAreaService } from './delivery-areas/delivery-areas.service';
import { PLAN_TYPE } from '../../enums/plan.enum';
import { StoreUpdatesService } from './services/store-updates.service';
import { StoreMigrationService } from './services/migrations.service';
import { BranchesService } from './branches/branches.service';
import { Item } from '../item/item.schema';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../enums/queues.enum';
import { Queue } from 'bull';
import { StoreJob } from './store.queue';
import { ChowdeckStoreService } from './services/3rd-parties/chowdeck.service';
import { PlanOption } from '../plan/plan-options/plan-options.schema';
import { SkipThrottle } from '@nestjs/throttler';
import { YearWrapService } from './year-wrap/year-wrap.service';
import { ONBOARDING_STEPS_WITH_REWARDS, User } from '../user/user.schema';
import { CreateStoreDtoV2 } from '../../models/dtos/StoreDtos';

@SkipThrottle()
@Controller()
export class StoreBroker {
  constructor(
    private readonly storeService: StoreService,
    private readonly branchService: BranchesService,
    private readonly storeUpdatesService: StoreUpdatesService,
    private readonly storeMigrationsService: StoreMigrationService,
    private readonly deliveryAreaService: DeliveryAreaService,
    private readonly storeChowdeckService: ChowdeckStoreService,
    private readonly yearWrapService: YearWrapService,
    @InjectModel(Store.name) private readonly storeModel: Model<StoreDocument>,
    @InjectModel(DeliveryArea.name) private readonly deliveryAreaModel: Model<DeliveryAreaDocument>,
    @InjectQueue(QUEUES.STORE)
    protected readonly storeQueue: Queue<StoreJob<any>>,
  ) {}

  @MessagePattern(BROKER_PATTERNS.STORE.ADD_VIEW)
  addView(filter: FilterQuery<StoreDocument>) {
    return this.storeService.addView(filter);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.CREATE_STORE_V2)
  createStoreV2(data: { user: User; store: CreateStoreDtoV2 }) {
    return this.storeService.createStoreV2(data.user, data.store);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_STORE)
  getStore(data: mongoose.FilterQuery<StoreDocument>) {
    return this.storeService.getStore(data);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_FIRST_STORE)
  async getFirstStore(filter: mongoose.FilterQuery<StoreDocument>) {
    return this.storeModel.findOne(filter).sort({ createdAt: 1 }).select('_id').lean();
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_STORE_LEAN)
  getStoreLean({ filter, select = {}, populate=[] }) {
    return this.storeService.getStoreLean(filter, select, populate);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.PAGINATE_STORES_LEAN)
  paginateStoresLean({ filter, count = 10, page = 1, select }) {
    return this.storeService.paginateStoresLean(filter, count, page, select);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.UPDATE_STORE)
  async updateStore({ filter, update }) {
    return this.storeModel.findOneAndUpdate(filter, update, { new: true });
  }

  @MessagePattern(BROKER_PATTERNS.STORE.ADD_CATEGORIES)
  async addCategories({ store, categories }) {
    return this.storeModel.findOneAndUpdate(
      { _id: store },
      { $push: { categories: { $each: categories } } },
      { new: true },
    );
  }

  @MessagePattern(BROKER_PATTERNS.STORE.AGGREGATE_STORE)
  aggregateStore(filter: any) {
    return this.storeService.aggregateStore(filter);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.COUNT_STORES)
  countStores(filter: any) {
    return this.storeService.countStores(filter);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.UPDATE_ITEMS_COUNT)
  async updateItemsCount({ store, inc }) {
    return await this.storeUpdatesService.updateItemsCount(store, inc);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.COUNT_SEO_STORES)
  countSeoStores() {
    return this.storeService.countSEOEligibeStores();
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_SITEMAP_PAGE)
  async getSitemapPage(data: { page: number; per_page: number }) {
    return this.storeService.getSitemapPage(data.page, data.per_page);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_STORES_LEAN)
  getStoresLean(data: { filter: any; select: any; pagination?: { skip: number; limit: number } }) {
    if (data?.filter?.name) {
      data.filter.$or = [
        ...(data.filter.$or ?? []),
        ...data.filter.name.split(' ').map((n) => ({ name: new RegExp(n, 'ig') })),
      ];
      delete data.filter.name;
    }

    return this.storeService.getStoresLean(data.filter, data.select, data.pagination);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_TOTAL)
  getStoreTotal(filter: FilterQuery<Store>) {
    return this.storeService.countStoreTotal(filter);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.DISABLE_STORE_FEATURES)
  disableStoreFeatures(data: {
    filter: FilterQuery<Store>;
    toPlanKey: PLAN_TYPE;
    isPaidSubscription: boolean;
    planOption: PlanOption;
  }) {
    return this.storeService.disableStoreFeatures(
      data.filter,
      data.isPaidSubscription,
      data.toPlanKey,
      data.planOption,
    );
  }

  @MessagePattern(BROKER_PATTERNS.STORE.ENABLE_STORE_FEATURES)
  enableStoreFeatures(data: {
    filter: FilterQuery<Store>;
    toPlanKey: PLAN_TYPE;
    isPaidSubscription: boolean;
    planOption: PlanOption;
  }) {
    return this.storeService.enableStoreFeatures(data.filter, data.isPaidSubscription, data.toPlanKey, data.planOption);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_DELIVERY_AREA)
  getDeliveryArea(filter: FilterQuery<DeliveryArea>) {
    return this.deliveryAreaService.getDeliveryArea(filter);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_DELIVERY_AREAS)
  getDeliveryAreas(areas: string[]) {
    return this.storeService.getDeliveryAreas(areas);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.ADD_STORE_SUBSCRIPTION)
  updateStoreSubscription(data: {
    store_id: string;
    subscription_id: string;
    planOption: PlanOption;
    isPaidSubscription: boolean;
    isFreeTrial: boolean;
    paidUpfront: boolean;
  }) {
    return this.storeUpdatesService.updateSubscription(
      data.store_id,
      data.subscription_id,
      data.planOption,
      data.isPaidSubscription,
      data.isFreeTrial,
      data.paidUpfront,
    );
  }

  @MessagePattern(BROKER_PATTERNS.STORE.VERIFY_SECURITY_PIN)
  verifySecurityPin(data: { store_id: string; security_pin: string }) {
    return this.storeUpdatesService.verifySecurityPin(data.store_id, data.security_pin);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.PAID_SUBSCRIPTION_STATUS)
  getPaidSubscriptionService(data: any) {
    return this.storeService.getHasPaidSubscription(data);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.COUNT_DELIVERY_AREAS)
  async countDeliveryAreas(store: string) {
    return await this.deliveryAreaModel.countDocuments({ store: store as any });
  }

  @MessagePattern(BROKER_PATTERNS.STORE.VALIDATE_STORE_OWNERSHIP)
  validateStoreOwnership(data: { userId: string; storeId: string }) {
    return this.storeService.validateStoreOwnership(data.userId, data.storeId);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_STORE_DELIVERY_AREAS)
  async getStoreDeliveryAddress(filter: FilterQuery<DeliveryArea>) {
    return await this.deliveryAreaModel.find(filter);
  }

  // ANALYTICS
  @MessagePattern(BROKER_PATTERNS.ANALYTICS.TOTAL_NUMBER_OF_STORES)
  async totalNumberOfStores() {
    return await this.storeService.storeCount();
  }

  @MessagePattern(BROKER_PATTERNS.ANALYTICS.TOTAL_STORES_CREATED_TODAY)
  async storesCreatedToday() {
    return await this.storeService.storesCreatedToday();
  }

  @MessagePattern(BROKER_PATTERNS.ANALYTICS.TOTAL_STORE_VISITS)
  async totalStoreVisits() {
    return await this.storeService.storeVisits();
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_CATEGORY_BY_ID)
  getCategoryById(store: string, category: string) {
    return this.storeService.getCatgeoryById(store, category);
  }
  @MessagePattern(BROKER_PATTERNS.STORE.GET_BRANCHES)
  getBranches({ id }) {
    return this.branchService.getBranch(id);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.AUTO_SYNC_ITEM_TO_CHOWDECK)
  async autoItemSyncToChowdeck(item: Item) {
    return await this.storeChowdeckService.autoItemSyncToChowdeck(item);
  }

  @MessagePattern(BROKER_PATTERNS.WHATSAPP_BOT.REFRESH_STORE_MENU)
  async refreshStoreMenuPdf(store: string) {
    return await this.storeQueue.add(
      QUEUES.STORE,
      {
        type: JOBS.REFRESH_STORE_MENU,
        data: {
          storeId: store,
        },
      },
      { delay: 1000 },
    );
  }

  @MessagePattern(BROKER_PATTERNS.STORE.GET_YEAR_WRAP)
  async getYearWrapByStoreSlug(data: { slug: string }) {
    return await this.yearWrapService.getYearWrapByStoreSlug(data.slug);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.UPDATE_FIRST_ORDER_WITH_PAYMENT)
  async updateFirstOrderWithPayment(storeId: string) {
    console.log(`Inside UPDATE_FIRST_ORDER_WITH_PAYMENT broker`);
    return this.storeUpdatesService.updateFirstOrderWithPayment(storeId);
  }

  @MessagePattern(BROKER_PATTERNS.STORE.COMPLETE_DOMAIN_PURCHASE)
  async completeDomainPurchase(data: { purchaseId: string; paymentId: string; nameservers?: string[] }) {
    const domainService = await this.storeService.getDomainService();
    return domainService.completeDomainPurchase(data.purchaseId, data.paymentId, data.nameservers);
  }
}
