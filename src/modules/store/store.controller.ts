import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  ApiBody,
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiExtraModels,
  ApiHeader,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiSecurity,
  ApiTags,
  ApiParam,
  ApiResponse,
} from '@nestjs/swagger';
import { RolePermissions, PlanPermissions } from '../../decorators/permission.decorator';
import { RoleGuard } from '../../guards/role.guard';
import { InternalApiKeyGuard } from '../../guards/internal-api-key.guard';
import { IRequest } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import {
  AddOrUpdateStoreCategoryStoreDto,
  ExportItemsToChowdeckDto,
  CreateStoreDto,
  FilterStoreDto,
  StoreCategoriesResponseDto,
  StoreResponseDto,
  StoreStatisticsResponse,
  StoreTopItemsResponse,
  UpdateCurrenciesDto,
  UpdateDirectCheckoutDto,
  UpdateSecurityPinDto,
  UpdateStoreDto,
  UpdateStoreMaintenanceModeDto,
  UpdateStorePaymentMethodsDto,
  UpdateStoreSlugDto,
  ImportChowdeckItemsDto,
  UpdateChowdeckSettingsDto,
  AdditionalStoreDetailsDto,
  FilterStoreAnalysisDto,
  CategoriesWithFirstItemResponseDto,
  UpdateStorefrontVersionDto,
  UpdateStoreStockThresholdDto,
  PendingActionsResponseDto,
  ToggleLowStockNotificationsDto,
} from '../../models/dtos/StoreDtos';
import { CheckoutChannels, WhatsappCheckoutChannel, Store } from './store.schema';
import { StoreService } from './services/store.service';

import { SCOPES } from '../../utils/permissions.util';
import { PlanGuard } from '../../guards/plan.guard';
import { checkIfUserOwnsStore, paymentsEnabledGuard, protectRoute } from '../../utils';
import { StoreUpdatesService } from './services/store-updates.service';
import { StoreMigrationService } from './services/migrations.service';
import { PaginatedStoreQueryDto, PaginatedStoreAnalysisQueryDto } from '../../models/dtos/PaginatedDto';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { COUNTRY_CODE } from '../country/country.schema';
import { BUSINESS_CATEGORIES } from './utils/store-categories';
import { StoreThirdPartyConnectionsService } from './services/3rd-party-connections.service';
import { ChowdeckStoreService } from './services/3rd-parties/chowdeck.service';
import MobileDetect from 'mobile-detect';
import { StoreJobService } from './services/store-job.service';
import { SkipThrottle } from '@nestjs/throttler';
import { BumpaStoreReverseService } from './services/3rd-parties/bumpa.service';

@Controller('stores')
@ApiTags('Store')
@ApiExtraModels(Store)
export class StoreController {
  constructor(
    private readonly storeService: StoreService,
    private readonly storeUpdatesService: StoreUpdatesService,
    private readonly storeMigrationsService: StoreMigrationService,
    private readonly storeThirdPartyConnectionsService: StoreThirdPartyConnectionsService,
    private readonly storeThirdPartyService: StoreThirdPartyConnectionsService,
    private readonly storeJobService: StoreJobService,
    private readonly storeChowdeckService: ChowdeckStoreService,
    private readonly bumpaReverseService: BumpaStoreReverseService,
  ) {}

  @Post('')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({ type: StoreResponseDto })
  async create(@Req() req: IRequest, @Body() storeReq: CreateStoreDto) {
    protectRoute(
      (await this.storeService.getUserStores(req.user.id)).length > 0,
      SCOPES.PLAN_PERMISSIONS.CAN_OWN_MULTIPLE_STORES,
      req.user,
      "You'll need switch to the business plus plan to create a new store",
    );

    const data = await this.storeService.create(req.user, storeReq, req.user.subscription !== null);
    return {
      message: '',
      data,
    };
  }

  @Get('/suggest-slug/:name')
  @ApiCreatedResponse({ type: StoreResponseDto })
  async getSuggestedSlug(@Req() req: IRequest, @Param('name') name: string) {
    const data = await this.storeService.generateSlug(name);

    return {
      message: '',
      data,
    };
  }

  @Get('/validate-slug/:slug')
  @ApiCreatedResponse({ type: StoreResponseDto })
  async validateSlug(@Req() req: IRequest, @Param('slug') slug: string) {
    const data = await this.storeService.verifyStoreSlug(slug);

    return {
      message: '',
      data,
    };
  }

  @Get('/seo')
  async storeSeoFetch() {
    return this.storeService.storeSeoFetch();
  }

  @Get('extractStoreImages')
  async extractStoreImages() {
    return this.storeService.extractStoreImageUrls();
  }

  @Put('additional-details')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async additionalDetails(@Req() req: IRequest, @Body() body: AdditionalStoreDetailsDto) {
    const data = await this.storeUpdatesService.additionalStoreDetails(req.user, req.user.store.id, body);
    return {
      message: 'Store Updated Successfully',
      data,
    };
  }

  @Get('/milestones')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getMilestones(@Req() req: IRequest) {
    const data = await this.storeService.getMilestones(req.user.store.id);
    return {
      data,
      message: 'Store milestones fetched',
    };
  }

  @Put('/toggle-low-stock-notifications')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Enable low stock notifications',
    description: 'Enable low stock notifications for all stores',
  })
  async toggleLowStockNotifications(@Req() req: IRequest, @Body() reqData: ToggleLowStockNotificationsDto) {
    const data = await this.storeUpdatesService.toggleLowStockNotifications(req.user.store.id, reqData);
    return {
      message: 'Store low stock notifications updated successfully',
      data,
    };
  }

  @Put(':id')
  @ApiSecurity('bearer')
  // @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async update(@Req() req: IRequest, @Param('id') storeId: string, @Body() storeReq: UpdateStoreDto) {
    // checkIfUserOwnsStore(req.user?.store as Store, storeId);
    const data = await this.storeUpdatesService.update(storeId, storeReq, req?.user ?? null);
    return {
      message: '',
      data,
    };
  }

  @Put(':id/color')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_CUSTOMIZE_COLOR)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async updateStoreColor(@Req() req: IRequest, @Param('id') storeId: string, @Body() reqData: { color: string }) {
    // checkIfUserOwnsStore(req.user?.store as Store, storeId);
    const data = await this.storeUpdatesService.updateStoreColor(storeId, reqData);
    return {
      message: '',
      data,
    };
  }

  @Get('/business-categories')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getBusinessCategories(@Req() req: IRequest) {
    return {
      data: Object.keys(BUSINESS_CATEGORIES),
      message: 'Business categories fetched',
    };
  }

  @Get('/business-categories/:category')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getCategoryProductTypes(@Req() req: IRequest, @Param('category') category: string) {
    return {
      data: BUSINESS_CATEGORIES[category] ?? [],
      message: 'Product types fetched',
    };
  }

  // Custom Domains
  @Get('/domains')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async getDomains(@Req() req: IRequest) {
    const data = await this.storeService.getDomains(req.user.store.id);
    return {
      message: 'Domains fetched successfully',
      data,
    };
  }

  @Get('/pending-actions')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: PendingActionsResponseDto })
  @ApiOperation({
    summary: 'Get pending actions for store',
    description: 'Returns items out of stock, unpaid orders, processing orders, low stock items, and pending orders',
  })
  async getPendingActions(@Req() req: IRequest) {
    const data = await this.storeService.getPendingActions(req.user.store.id);
    return {
      message: 'Pending actions retrieved successfully',
      data,
    };
  }

  @Get(':id')
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: StoreResponseDto })
  async get(@Param('id') id: string) {
    const data = await this.storeService.getWithId(id);
    return {
      message: '',
      data,
    };
  }

  @Put('/:id/slug')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_CUSTOMIZE_LINK)
  // @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async internalUpdate(@Req() req: IRequest, @Param('id') id: string, @Body() storeReq: UpdateStoreSlugDto) {
    checkIfUserOwnsStore(req.user?.store as Store, id);
    const data = await this.storeUpdatesService.updateSlug(req.user.id, id, storeReq.slug);
    return {
      message: '',
      data,
    };
  }

  @Put('/:id/currencies')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_MANAGE_CURRENCIES)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async updateCurrencies(@Req() req: IRequest, @Param('id') id: string, @Body() reqData: UpdateCurrenciesDto) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);

    const data = await this.storeUpdatesService.updateStoreCurrencies(id, reqData);

    return {
      message: 'Currencies updated successfully',
      data,
    };
  }

  @Put('/:id/categorization')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async updateStoreCategorization(
    @Req() req: IRequest,
    @Param('id') id: string,
    @Body() reqData: Store['business_category'],
  ) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);

    const data = await this.storeUpdatesService.updateStoreCategorizations(id, reqData);

    return {
      message: 'Categories updated successfully',
      data,
    };
  }

  @Put('/:id/auto-check-in')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async updateAutoCheckInConfig(
    @Req() req: IRequest,
    @Param('id') id: string,
    @Body() reqData: Store['configuration']['auto_customer_check_in'],
  ) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);

    const data = await this.storeUpdatesService.updateAutoCheckInConfig(id, reqData);
    return {
      message: 'Categories updated successfully',
      data,
    };
  }

  @Get('/public/:slug')
  @SkipThrottle()
  @ApiOkResponse({ type: StoreResponseDto })
  @ApiNotFoundResponse({
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async getStoreBySlug(
    @Param('slug') slug: string,
    @Query('xadmin') xadmin: string,
    @Query('password') password: string,
  ) {
    const data = await this.storeService.getStoreBySlug(slug, xadmin, password);

    return {
      message: 'Store fetched successfully',
      data,
    };
  }

  @Get('')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: StoreResponseDto })
  async getStores(@Query('pagination') pagination: PaginatedStoreQueryDto, @Query('filter') filter: FilterStoreDto) {
    const data = await this.storeService.getStores(filter, pagination);
    return {
      message: '',
      ...data,
    };
  }

  @Get('/admin/analysis')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: StoreResponseDto })
  async getStoreAnalysis(
    @Query('pagination') pagination: PaginatedStoreAnalysisQueryDto,
    @Query('filter') filter: FilterStoreAnalysisDto,
  ) {
    const data = await this.storeService.getStoresData(filter, pagination);
    return {
      message: '',
      ...data,
    };
  }

  @Get('/admin/analysis/analytics')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: StoreResponseDto })
  async getStoreAnalysisAnalytics() {
    const data = await this.storeService.getAnalysisAnalytics();

    return {
      message: 'Analytics fetched successfully',
      data,
    };
  }

  @Get('/admin/analysis/subscribers')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: StoreResponseDto })
  async getStoreAnalysisForSubscribers(
    @Query('pagination') pagination: PaginatedStoreAnalysisQueryDto,
    @Query('filter') filter: FilterStoreAnalysisDto,
    @Query('active_subscribers') active_subscribers: string,
  ) {
    const data = await this.storeService.getStoresDataForSubscribers(filter, pagination, active_subscribers === 'true');
    return {
      message: '',
      ...data,
    };
  }

  // @Get('/test/test-summaries')
  // async getTestSummary() {
  //   return await this.storeJobService.getTestSummaries();
  // }

  @Post(':id/categories')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'create a store category' })
  @ApiOkResponse({
    status: HttpStatus.CREATED,
    type: [StoreCategoriesResponseDto],
  })
  @ApiBody({ type: [AddOrUpdateStoreCategoryStoreDto] })
  @HttpCode(HttpStatus.OK)
  async addOrUpdateCategories(
    @Req() req: IRequest,
    @Param('id') storeId: string,
    @Body() body: AddOrUpdateStoreCategoryStoreDto[],
  ) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, storeId);
    const data = await this.storeUpdatesService.addOrUpdateCategoriesOptimized(storeId, body);
    return {
      message: '',
      data,
    };
  }

  @Get(':id/categories')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'get store categories' })
  @ApiOkResponse({ status: HttpStatus.OK, type: StoreCategoriesResponseDto })
  @HttpCode(HttpStatus.OK)
  async getCategories(@Req() req: IRequest, @Param('id') storeId: string) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, storeId);
    const data = await this.storeService.getCategories(storeId);
    return {
      message: '',
      data,
    };
  }

  @Get(':id/categories-with-items')
  @ApiSecurity('bearer')
  @ApiOperation({ summary: 'Get store categories with the first item in each category' })
  @ApiOkResponse({ status: HttpStatus.OK, type: CategoriesWithFirstItemResponseDto })
  @HttpCode(HttpStatus.OK)
  async getCategoriesWithFirstItem(@Req() req: IRequest, @Param('id') storeId: string) {
    const data = await this.storeService.getCategoriesWithFirstItem(storeId);
    return {
      message: 'Categories with first items fetched successfully',
      data,
    };
  }

  @Delete(':id/categories/:category_id')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ status: HttpStatus.OK })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCategory(@Req() req: IRequest, @Param('id') storeId: string, @Param('category_id') categoryId: string) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, storeId);
    return await this.storeService.deleteCategory(storeId, categoryId);
  }

  @Put(':id/checkout-channels')
  @ApiSecurity('bearer')
  // @RolePermissions(SCOPES.SETTINGS.UPDATE_STORE_DETAILS)
  @UseGuards(JwtAuthGuard)
  async setCheckoutChannels(@Req() req: IRequest, @Param('id') storeId: string, @Body() body: CheckoutChannels) {
    checkIfUserOwnsStore(req.user?.store as Store, storeId);
    const data = await this.storeUpdatesService.setCheckoutChannels(storeId, body);

    return {
      message: '',
      data,
    };
  }

  @Get(':id/checkout-channels')
  async getGeckoutChannels(@Param('id') storeId: string) {
    const data = await this.storeService.getCheckoutChannels(storeId);

    return {
      message: '',
      data,
    };
  }

  @Get(':id/statistics')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get store statistics' })
  @ApiOkResponse({ status: HttpStatus.OK, type: StoreStatisticsResponse })
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
        is_mobile: {
          type: 'boolean',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  async getStatistics(@Req() req: IRequest, @Param('id') id: string, @Query() query: any) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    const filter = query.filter;
    const is_mobile = query.is_mobile === 'true';

    // Detect if the request is from a mobile device
    // const md = new MobileDetect(req.headers['user-agent']);
    // const is_mobile = !!md.mobile(); // returns true if it's a mobile device

    const data = await this.storeService.getStatistics(id, filter, is_mobile);
    return {
      message: '',
      data,
    };
  }

  @Post('/domains')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Add a custom domain to a store' })
  @ApiBody({
    type: 'object',
    schema: {
      properties: {
        domain: { type: 'string', example: 'example.com', description: 'Domain to map to the store' },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Domain successfully added' })
  @ApiResponse({ status: 400, description: 'Invalid domain or domain already in use' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - user does not own this store' })
  async addDomain(@Req() req: IRequest, @Body() body: { domain: string }) {
    const data = await this.storeService.addDomain(req.user.store.id, body.domain);

    return {
      message: 'Domain added successfully',
      data,
    };
  }

  @Delete('/domains/:id')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Remove a domain from a store and revoke its SSL certificate' })
  @ApiParam({
    name: 'id',
    description: 'ID of the domain to remove',
    type: 'string',
    required: true,
  })
  @ApiResponse({ status: 200, description: 'Domain successfully removed' })
  @ApiResponse({ status: 400, description: 'Invalid domain ID' })
  @ApiResponse({ status: 403, description: 'Domain does not belong to your store' })
  @ApiResponse({ status: 404, description: 'Domain not found' })
  @ApiResponse({ status: 500, description: 'Failed to remove domain' })
  async removeDomain(@Req() req: IRequest, @Param('id') domainId: string) {
    // Validate that the domain belongs to the user's store
    const domains = await this.storeService.getDomains(req.user.store.id);
    const domainBelongsToStore = domains.some((domain) => domain._id.toString() === domainId);

    if (!domainBelongsToStore) {
      throw new ForbiddenException('This domain does not belong to your store.');
    }

    const data = await this.storeService.removeDomain(req.user.store.id, domainId);
    return {
      message: 'Domain removed successfully',
      data,
    };
  }

  @Post('/domains/:id/verify')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async verifyDomain(@Req() req: IRequest, @Param('id') domainId: string) {
    // Validate that the domain belongs to the user's store
    const domains = await this.storeService.getDomains(req.user.store.id);
    const domainBelongsToStore = domains.some((domain) => domain._id.toString() === domainId);

    if (!domainBelongsToStore) {
      throw new ForbiddenException('This domain does not belong to your store.');
    }

    const result = await this.storeService.verifyDomain(domainId);

    return {
      message: result.message,
      success: result.success,
      data: {
        verified: result.verified,
        certificateGenerated: result.certificateGenerated,
        certificateMessage: result.certificateMessage,
      },
    };
  }

  @Post('/domains/:id/generate-certificate')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  async generateSslCertificate(@Req() req: IRequest, @Param('id') domainId: string) {
    // Validate that the domain belongs to the user's store
    const domains = await this.storeService.getDomains(req.user.store.id);
    const domainBelongsToStore = domains.some((domain) => domain._id.toString() === domainId);

    if (!domainBelongsToStore) {
      throw new ForbiddenException('This domain does not belong to your store.');
    }

    const result = await this.storeService.generateSslCertificate(domainId);

    return {
      message: result.message,
      success: result.success,
      data: null,
    };
  }

  @Get(':id/email-summaries')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get store statistics' })
  @ApiOkResponse({ status: HttpStatus.OK, type: StoreStatisticsResponse })
  @ApiQuery({
    name: 'filter',
    required: false,
    style: 'deepObject',
    explode: true,
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
          description: 'Start date',
        },
        to: {
          type: 'string',
          format: 'date',
          description: 'End date',
        },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  async getEmailSummaries(@Req() req: IRequest, @Param('id') id: string, @Query() query: any) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    const filter = query.filter;

    const data = await this.storeService.getEmailSummaries(id, filter, 'month');
    return {
      message: 'Email summaries gotten',
      data,
    };
  }

  @Get(':id/statistics/top-items')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get store top items' })
  @ApiOkResponse({ status: HttpStatus.OK, type: StoreTopItemsResponse })
  @HttpCode(HttpStatus.OK)
  async getStoreTopItems(@Req() req: IRequest, @Param('id') id: string) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    const data = await this.storeService.getTopItems(id);

    return {
      message: '',
      data,
    };
  }

  @Get(':id/invoice-statistics')
  @UseGuards(JwtAuthGuard)
  async getInvoiceStatistics(@Req() req: IRequest, @Param('id') id: string) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    const stats = await this.storeService.getInvoiceStatistics(id);

    return {
      message: 'Invoice statistics gotten',
      data: stats,
    };
  }

  // Chowdeck Controllers

  @Put(':id/chowdeck-config')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_USE_CHOWDECK)
  @UseGuards(JwtAuthGuard, PlanGuard)
  async updateChowdeckConfig(
    @Req() req: IRequest,
    @Param('id') storeId: string,
    @Body() body: UpdateChowdeckSettingsDto,
  ) {
    checkIfUserOwnsStore(req.user?.store as Store, storeId);
    const data = await this.storeUpdatesService.updateChowdeckConfig(storeId, body);

    return {
      message: 'Updated chowdeck config',
      data,
    };
  }

  @Post('chowdeck')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async addChowdeckReferenceToStore(
    @Req() req: IRequest,
    @Body() body: { storeId: string; reference: string; private_key: string },
  ) {
    await this.storeChowdeckService.addChowdeckReferenceToStore(body.storeId, body.reference, body.private_key);
    return {
      message: 'Chowdeck linked to store',
      data: {},
    };
  }

  @Post('chowdeck/create-merchant')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async createChowdeckMerchant(@Req() req: IRequest, @Body() body: { storeId: string }) {
    await this.storeChowdeckService.createChowdeckMerchant(body.storeId);
    return {
      message: 'Created chowdeck merchant',
      data: {},
    };
  }

  @Get('chowdeck/menu-items')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_USE_CHOWDECK)
  @UseGuards(JwtAuthGuard, PlanGuard)
  async getChowdeckItems(@Req() req: IRequest) {
    const store = req.user.store as Store;
    const data = await this.storeChowdeckService.getChowdeckItems(store?.id);

    return {
      message: 'Successfully fetched chowdeck items',
      data,
    };
  }

  @Post('chowdeck/export-items')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_USE_CHOWDECK)
  @UseGuards(JwtAuthGuard, PlanGuard)
  async exportItemsToChowdeck(@Req() req: IRequest, @Body() dto: ExportItemsToChowdeckDto) {
    const store = req.user.store as Store;
    const data = await this.storeChowdeckService.exportItemsToChowdeck(store?.id, dto);

    return {
      message: 'Successfully created Chowdeck-export job',
      data,
    };
  }

  @Post('chowdeck/import-items')
  @ApiSecurity('bearer')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_USE_CHOWDECK)
  @UseGuards(JwtAuthGuard, PlanGuard)
  async importChowdeckItems(@Req() req: IRequest, @Body() dto: ImportChowdeckItemsDto) {
    const store = req.user.store as Store;
    const data = await this.storeChowdeckService.importChowdeckItems(store?.id, dto);

    return {
      message: 'Successfully created Chowdeck-Import job',
      data,
    };
  }

  @Put(':id/maintenance-mode')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_MAINTENANCE_MODE)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async updateMaintenanceMode(
    @Req() req: IRequest,
    @Param('id') id: string,
    @Body() body: UpdateStoreMaintenanceModeDto,
  ) {
    checkIfUserOwnsStore(req.user?.store as Store, id);
    const data = await this.storeUpdatesService.updateStoreMaintenanceMode(id, body);
    return {
      message: 'Store maintenance mode updated successfully',
      data,
    };
  }

  /** PAYMENT & INVOICES */

  @Put(':id/security-pin')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_SECURITY_PIN)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async updateSecurityPin(@Req() req: IRequest, @Param('id') id: string, @Body() body: UpdateSecurityPinDto) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    const data = await this.storeUpdatesService.updateSecurityPin(req.user.id, store.id, body);
    return {
      message: 'Security pin updated successfully',
      data,
    };
  }

  @Put('/:id/direct-checkout')
  @ApiSecurity('bearer')
  @RolePermissions(SCOPES.SETTINGS.ENABLE_DIRECT_CHECKOUT)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async updateDirectCheckout(@Req() req: IRequest, @Param('id') id: string, @Body() reqData: UpdateDirectCheckoutDto) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    paymentsEnabledGuard(store);

    const data = await this.storeUpdatesService.updateDirectCheckout(id, reqData);

    return {
      message: "You've successfully updated direct checkout",
      data,
    };
  }

  @Get('/:id/bank-account')
  async getStoreBankAccount(@Param('id') id: string) {
    const data = await this.storeService.getStoreBankAccount(id);

    return {
      message: 'Bank account fetched successfully',
      data,
    };
  }

  @Get('/:id/bank-accounts')
  async getStoreBankAccounts(@Param('id') id: string) {
    const data = await this.storeService.getStoreBankAccounts(id);

    return {
      message: 'Bank accounts fetched successfully',
      data,
    };
  }

  @Put(':id/init-payment-methods')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async initPaymentMethods(@Req() req: IRequest, @Param('id') storeId: string) {
    const data = await this.storeUpdatesService.initPaymentMethods(storeId);

    return {
      message: 'Payment methods initiated',
      data,
    };
  }

  @Put(':id/menu-image')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_PAYMENT_METHODS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiSecurity('bearer')
  async getMenuImage(@Req() req: IRequest, @Param('id') id: string) {
    const store = req.user.store as Store;
    checkIfUserOwnsStore(store, id);
    paymentsEnabledGuard(store);

    const pdf = await this.storeUpdatesService.generateMenu(id);

    return {
      message: 'Successfully generated menu',
      data: {
        pdf,
      },
    };
  }

  //should have country guard
  @Put(':id/payment-methods')
  @RolePermissions(SCOPES.SETTINGS.UPDATE_PAYMENT_METHODS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  async updatePaymentMethod(@Req() req: IRequest, @Param('id') id: string, @Body() body: UpdateStorePaymentMethodsDto) {
    checkIfUserOwnsStore(req.user?.store as Store, id);
    paymentsEnabledGuard(req.user?.store as Store);
    const data = await this.storeUpdatesService.updateStorePaymentMethods(id, body);

    return {
      message: 'Store payment method updated',
      data,
    };
  }

  /** INSTAGRAM INTEGRATION */

  @Get('ig/access-token')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async generateInstagramAccessToken(
    @Req() req: IRequest,
    @Query('access_code') access_code: string,
    @Query('redirect_uri') redirect_uri: string,
  ) {
    const data = await this.storeThirdPartyConnectionsService.generateInstagramAccessToken(
      access_code,
      redirect_uri,
      req.user.store.id,
    );

    return {
      data,
      message: 'Successfully retrieved instagram access token',
    };
  }

  @Get('ig/disconnect')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async disconnectInstagram(@Req() req: IRequest) {
    const result = await this.storeThirdPartyConnectionsService.disconnectInstagram(req.user.store.id);
    return {
      message: 'Successfully disconnected Instagram',
      data: result,
    };
  }

  @Get('ig/media')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getInstagramMedia(@Req() req: IRequest, @Query('pagination') pagination: any) {
    const data = await this.storeThirdPartyConnectionsService.getInstagramMedia(req.user.store.id, pagination);

    return {
      data,
      message: 'Successfully retrieved instagram media',
    };
  }

  @Get('ig/post-medias')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getInstagramAllMediaFromPosts(@Req() req: IRequest, @Query('pagination') pagination: any) {
    const data = await this.storeThirdPartyConnectionsService.getAllInstagramMediaFromPosts(
      req.user.store.id,
      pagination,
    );

    return {
      ...data,
      message: 'Successfully retrieved instagram media',
    };
  }

  @Get('ig/album-media/:id')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getInstagramAlbumMedia(@Req() req: IRequest, @Param('id') mediaId: string) {
    const data = await this.storeThirdPartyConnectionsService.getInstagramAlbumMedia(req.user.store.id, mediaId);
    return {
      data,
      message: 'Successfully retrieved instagram album media',
    };
  }

  @Post('ig/albums-media')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getAlbumsMedia(@Req() req: IRequest, @Body() body: { media_ids: string[] }) {
    const data = await this.storeThirdPartyConnectionsService.getMultipleInstagramAlbumMedia(
      req.user.store.id,
      body.media_ids,
    );

    return {
      data,
      message: 'Successfully retrieved instagram album media',
    };
  }

  @Get('ig/check-token')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async checkInstagramToken(@Req() req: IRequest) {
    await this.storeThirdPartyConnectionsService.checkInstagramToken(req.user.store.id);
    return {
      message: 'Successfully checked instagram access token',
    };
  }
  @Get('ig/user')
  @ApiSecurity('bearer')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  async getInstagramUser(@Req() req: IRequest) {
    const data = await this.storeThirdPartyConnectionsService.getInstagramUser(req.user.store.id);
    return {
      message: 'Successfully fetched instagram user details',
      data,
    };
  }

  /** MIGRATIONS **/

  //Enable payments for any stores created from 1ST SEPT 2024, also updating their products to have a default quantity of 10
  @Post('/enable-payments-for-latest-stores')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async enableStorePayments() {
    const response = await this.storeMigrationsService.enablePaymentsForLatestStores();
    return response;
  }

  @Post('/migrate-country-names')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateCountryNames() {
    await this.storeMigrationsService.migrateStoreCountryFormats();
    return {
      message: 'migrated countries',
      data: {},
    };
  }

  @Post('/migrate-missing-slugs')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateMissingSlugs() {
    const stores = await this.storeMigrationsService.migrateMissingSlugs();
    return {
      message: 'migrated missing slugs',
      data: stores,
    };
  }

  @Post('/migrate-store-numbers')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreNumbers() {
    await this.storeMigrationsService.migrateStorePhoneNumbers();
    return {
      message: 'migrated stores',
      data: {},
    };
  }

  @Post('/migrate-owner-subscriptions')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateOwnerSubscriptions() {
    const data = await this.storeMigrationsService.migrateOwnerSubscriptionsToStore();
    return {
      message: 'migrated subscriptions',
      data,
    };
  }

  @Post('/migrate-new-checkout-channels')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateNewCheckoutChannels() {
    const data = await this.storeMigrationsService.migrateCheckoutChannels();

    return {
      message: 'migrated Checkout channels',
      data,
    };
  }

  @Put(':id/admin-update')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async adminUpdateStore(@Param('id') id: string, @Body() storeReq: UpdateStoreDto) {
    const data = await this.storeService.adminUpdateStore(id, storeReq);

    return {
      message: '',
      data,
    };
  }

  @Post('/migrate-store-checkout-channels-primary')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreCheckoutChannelsWithPrimary() {
    const stores = await this.storeMigrationsService.migrateCheckoutChannelsWithPrimary();
    return {
      message: 'migrated store checkout channels',
      data: stores,
    };
  }

  @Post('/migrate-store-checkout-channels')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreCheckoutChannels() {
    await this.storeMigrationsService.migrateStoreNumbersToCheckoutChannels();
    return {
      message: 'migrated store checkout channels',
      data: {},
    };
  }

  @Post('/migrate-has-paid-subscription')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateHasPaidSubscription() {
    return await this.storeMigrationsService.updateStoresHasPaidSubscriptionBatch();
  }

  @Post('/migrate-store-current-plan')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreCurrentPlan(@Query('page') page: number) {
    return await this.storeMigrationsService.updateStoresCurrentPlanBatch(page);
  }

  @Post('/local-migration')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async localMigration() {
    const data = await this.storeMigrationsService.localMigration();

    return {
      message: 'local migration complete',
    };
  }

  @Post('/migrate-new-payment-methods')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async migrateNewPaymentMethods(@Body() body: { country: COUNTRY_CODE }) {
    const data = await this.storeMigrationsService.migrateNewPaymentOptions(body.country);

    return {
      message: 'Added new methods to payment enabled stores',
      data,
    };
  }

  @Post('/delete-mono-on-prod')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async deleteMonoOnProd(@Body() body: { country: COUNTRY_CODE }) {
    const data = await this.storeMigrationsService.deleteMonoOnProd(body.country);

    return {
      message: 'Deleted mono',
      data,
    };
  }

  @Post('/migrate-store-product-config')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreProductConfig() {
    const data = await this.storeMigrationsService.migrateStoreProductConfig();

    return {
      message: 'migrated store product config',
      data,
    };
  }

  @Post('/migrate-store-currencies')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreCurrencies() {
    const data = await this.storeMigrationsService.migrateStoreCurrencies();

    return {
      message: 'migrated store currencies',
      data,
    };
  }

  @Post('/migrate-store-wallets')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreWallets() {
    const data = await this.storeMigrationsService.migrateStoreWallets();

    return {
      message: 'migrated store wallets',
      data,
    };
  }

  @Post('/migrate-store-payment-methods')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStorePaymentMethods() {
    const data = await this.storeMigrationsService.migrateStorePaymentMethods();

    return {
      message: 'migrated store payment methods',
      data,
    };
  }

  @Post('/migrate-checkout-config')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateCheckoutConfig() {
    const data = await this.storeMigrationsService.migrateStoreCheckoutConfig();

    return {
      message: 'migrated store checkout configs',
      data,
    };
  }

  @Post('/migrate-custom-checkout-form-options')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateCustomCheckoutFormOptions() {
    const data = await this.storeMigrationsService.migrateCustomCheckoutFormOptions();

    return {
      message: 'migrated store checkout configs',
      data,
    };
  }

  @Post('/migrate-items-count')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateItemsCount() {
    const data = await this.storeMigrationsService.migrateStoreItemsCount();

    return {
      message: 'migrated store items count',
      data,
    };
  }

  @Post('/migrate-store-currencies-from-wallet')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreCurrenciesFromWallet() {
    const data = await this.storeMigrationsService.migrateStoreCurrenciesFromWallets();

    return {
      message: 'migrated store currencies',
      data,
    };
  }

  @Post('/migrate-has-taken-order-with-payment')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreHasTakenOrderWithPayment(@Query('page') page: number) {
    const data = await this.storeMigrationsService.migrateStoreHasTakenOrderWithPayment(page);

    return {
      message: 'migrated store has taken order with payment',
      data,
    };
  }

  @Post('/migrate-store-on-free-trial-and-paid-upfront')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async migrateStoreOnFreeTrialAndPaidUpfront() {
    return await this.storeMigrationsService.migrateStoresOnFreeTrialAndPaidUpfront();
  }

  @Post('/regenerate-store-menus')
  @ApiSecurity('bearer')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async generateStoreMenus(@Body() body: { stores: string[] }) {
    const data = await this.storeMigrationsService.regenerateStoreMenus(body.stores);
    return {
      message: 'regenerated store menus',
      data,
    };
  }

  @Post('/update-store-images-urls-to-new-s3-migration')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  @ApiHeader({ name: 'x-internal-api-key' })
  async updateStoreImagesUrlsToNewS3Migration() {
    this.storeMigrationsService.updateStoreImagesUrlsToNewS3Migration();
    return {
      message: 'Image urls update to new s3 migration in progress. Please wait for images urls',
    };
  }

  @Get('/domain/:domain')
  @ApiOkResponse({ type: StoreResponseDto })
  @ApiNotFoundResponse({
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
        },
      },
    },
  })
  async getStoreByDomain(
    @Param('domain') domain: string,
    @Query('xadmin') xadmin: string,
    @Query('password') password: string,
  ) {
    const data = await this.storeService.getStoreByDomain(domain, xadmin, password);

    return {
      message: 'Store fetched successfully',
      data,
    };
  }

  @Put('flags/storefront-version')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiOkResponse({ type: StoreResponseDto })
  async updateStorefrontVersion(@Req() req: IRequest, @Body() body: UpdateStorefrontVersionDto) {
    const data = await this.storeUpdatesService.updateStorefrontVersion(req.user.store.id, body.version);
    return {
      message: 'Storefront version updated successfully',
      data,
    };
  }

  @Get('/reverse/bumpa-store')
  async reverseBumpaStore(@Query('bumpaUrl') bumpaUrl: string) {
    if (!bumpaUrl) {
      throw new BadRequestException('bumpaUrl is required');
    }
    const data = await this.bumpaReverseService.getCatlogStoreFromBumpaUrl(bumpaUrl);
    return { message: 'Bumpa store fetched', data };
  }

  @Get('/reverse/bumpa-items')
  async reverseBumpaItems(
    @Query('bumpaUrl') bumpaUrl: string,
    @Query('page') page?: string,
    @Query('per_page') per_page?: string,
    @Query('search') search?: string,
  ) {
    if (!bumpaUrl) {
      throw new BadRequestException('bumpaUrl is required');
    }
    const pageNum = Number(page) || 1;
    const perPageNum = Number(per_page) || 15;
    const data = await this.bumpaReverseService.getBumpaProductsFromBumpaUrl(bumpaUrl, pageNum, perPageNum, search);
    return { message: 'Bumpa products fetched', data };
  }
}
