import { Injectable, Logger } from '@nestjs/common';
import { BumpaReverseRepository } from '../../../../repositories/bumpa-reverse/index.repository';
import { productFromBumpa, storeFromBumpa } from '../../../../repositories/bumpa-reverse/utils';
import { Store } from '../../store.schema';
import { BumpaProductsResponse } from '../../../../repositories/bumpa-reverse/types';
import { Item } from '../../../item/item.schema';

@Injectable()
export class BumpaStoreReverseService {
  constructor(private readonly logger: Logger, private readonly bumpaRepo: BumpaReverseRepository) {}

  public async getCatlogStoreFromBumpaUrl(bumpaUrl: string): Promise<Partial<Store>> {
    const baseUrl = bumpaUrl?.trim();
    if (!baseUrl) {
      throw new Error('bumpaUrl is required');
    }

    const [{ data: collections }, { data: storeRoot }] = await Promise.all([
      this.bumpaRepo.getCollections(baseUrl),
      this.bumpaRepo.getStore(baseUrl),
    ]);

    if (!storeRoot) {
      throw new Error('Failed to fetch Bumpa store');
    }

    const tags = collections?.tags ?? [];
    const catlogStore = storeFromBumpa(storeRoot.store, storeRoot.theme, storeRoot.layout, tags);
    return catlogStore;
  }

  public async getBumpaProductsFromBumpaUrl(bumpaUrl: string, page = 1, per_page = 15, search?: string) {
    const baseUrl = bumpaUrl?.trim();
    if (!baseUrl) {
      throw new Error('bumpaUrl is required');
    }

    const { data } = await this.bumpaRepo.getProducts(baseUrl, { page, limit: per_page, search });
    if (!data) {
      throw new Error('Failed to fetch Bumpa products');
    }

    const catlogItems = data.products.data.map((product) => productFromBumpa(product)) as Item[];

    return {
      items: catlogItems,
      total: data.products.total,
      page,
      per_page,
    };
  }
}
