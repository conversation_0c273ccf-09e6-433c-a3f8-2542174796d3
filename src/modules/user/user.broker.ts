import * as mongoose from 'mongoose';
import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';

import { User, UserDocument } from './user.schema';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { UserService } from './user.service';
import { CreateUserDto } from '../../models/dtos/UserDtos';
import { ReferralsService } from './referrals/referrals.service';
import { SkipThrottle } from '@nestjs/throttler';
import { NOTIFICATION_TYPE } from './notifications/notification.schema';

@SkipThrottle()
@Controller()
export class UserBroker {
  constructor(private readonly userService: UserService, private readonly referralsService: ReferralsService) {}

  @MessagePattern(BROKER_PATTERNS.USER.GET_USER)
  getUser(data: mongoose.FilterQuery<UserDocument>) {
    return this.userService.getUser(data);
  }

  @MessagePattern(BROKER_PATTERNS.USER.CREATE_USER)
  createUser(data: CreateUserDto) {
    return this.userService.create(data);
  }

  @MessagePattern(BROKER_PATTERNS.USER.GET_USERS)
  getUsers({ data, select }: { data: mongoose.FilterQuery<UserDocument>; select?: any }) {
    return this.userService.getUser(data, true, select);
  }

  @MessagePattern(BROKER_PATTERNS.USER.AGGREGATE_USERS)
  aggregateUsers(data: any[]) {
    return this.userService.aggregateUser(data);
  }

  @MessagePattern(BROKER_PATTERNS.USER.UPDATE_USER)
  updateUser(data: { filter: mongoose.FilterQuery<UserDocument>; update: UserDocument }) {
    return this.userService.brokerUpdateUser(data.filter, data.update);
  }

  @MessagePattern(BROKER_PATTERNS.USER.MARK_USER_AS_QUALIFIED)
  markUserAsQualified(data: { userId: string }) {
    return this.userService.markUserAsQualified(data.userId);
  }

  @MessagePattern(BROKER_PATTERNS.USER.ADD_STORE)
  async addStore({ id, storeId, isPrimaryStore = false }) {
    return this.userService.addStore(id, storeId, isPrimaryStore);
  }

  @MessagePattern(BROKER_PATTERNS.USER.REDEEM_REFERRAL_CREDITS)
  async redeemReferralCredits({ userId, country }) {
    return this.userService.redeemReferralCredits(userId, country);
  }

  @MessagePattern(BROKER_PATTERNS.USER.REFERRAL_SUBSCRIBED)
  async referralSubscribed({ user, referredUser, timesClaimed }) {
    return this.referralsService.referralSubscribed(user, referredUser, timesClaimed);
  }

  @MessagePattern(BROKER_PATTERNS.USER.REFERRAL_WRAP_DATA)
  async referralWrapData({ ownerId, startDate, endDate }) {
    return this.referralsService.referralsWrapData(ownerId, startDate, endDate);
  }

  @MessagePattern(BROKER_PATTERNS.USER.GET_REFERRALS)
  async getStoreReferrals(userId) {
    return this.referralsService.getUserReferrals(userId);
  }

  @MessagePattern(BROKER_PATTERNS.USER.REMOVE_STORE)
  async removeStore({ id, storeId }) {
    return this.userService.removeStore(id, storeId);
  }
  @MessagePattern(BROKER_PATTERNS.USER.VERIFY_PASSWORD)
  async verifyPassword({ id, password }) {
    return this.userService.verifyPassword(id, password);
  }

  @MessagePattern(BROKER_PATTERNS.USER.VERIFY_JWT_TOKEN)
  async verifyUserToken({ token }) {
    return this.userService.verifyJwtToken(token);
  }

  @MessagePattern(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION)
  async sendPushNotification({ store, owner_only, message, notification_type, data }) {
    return this.userService.sendPushNotification(
      store,
      message,
      owner_only,
      undefined, // user param is undefined when sending to store members
      notification_type || NOTIFICATION_TYPE.GENERIC,
      data,
    );
  }

  @MessagePattern(BROKER_PATTERNS.USER.GENERATE_JWT_TOKEN)
  async generateJwtToken({ user, storeId }: { user: UserDocument; storeId: string }) {
    return this.userService.generateJwtToken(user, storeId);
  }

  // Analytics

  @MessagePattern(BROKER_PATTERNS.ANALYTICS.TOTAL_USERS)
  async totalUsers() {
    return this.userService.userCount();
  }

  @MessagePattern(BROKER_PATTERNS.USER.CUSTOMER_IO_STATUS)
  updateCustomerIoStatus({ id, status }: { id: string; status: boolean }) {
    return this.userService.updateCustomerIoStatus(id, status);
  }

  @MessagePattern(BROKER_PATTERNS.USER.SEND_CONVERSION_EVENT_TO_META)
  sendConversionEventToMeta({ userId, eventName, eventId }: { userId: string; eventName: string; eventId: string }) {
    return this.userService.sendConversionEventToMeta(userId, eventName, eventId);
  }
}
