import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { COUNTRY_CODE } from '../../country/country.schema';

export type NotificationDocument = Notification & Document;
export type PushNotificationSubscriptionDocument = PushNotificationSubscription & Document;
export type FirebaseSubscriptionDocument = FirebaseSubscription & Document;

export enum NOTIFICATION_TYPE {
  GENERIC = 'GENERIC',
  NEW_ORDER = 'NEW_ORDER',
  PAYMENT_RECEIVED_ORDER = 'PAYMENT_RECEIVED_ORDER',
  PAYMENT_RECEIVED_INVOICE = 'PAYMENT_RECEIVED_INVOICE',
  PAYMENT_RECEIVED_BANK = 'PAYMENT_RECEIVED_BANK',
  KYC_APPROVED = 'KYC_APPROVED',
  KYC_DECLINED = 'KYC_DECLINED',
  WITHDRAWAL = 'WITHDRAWAL',
  SUBSCRIPTION_EXPIRING = 'SUBSCRIPTION_EXPIRING',
  TRIAL_EXPIRING = 'TRIAL_EXPIRING',
  SUBSCRIPTION_CANCELLED = 'SUBSCRIPTION_CANCELLED',
  MILESTONE_PAYMENT = 'MILESTONE_PAYMENT',
  MILESTONE_VISIT = 'MILESTONE_VISIT',
  MILESTONE_ORDER = 'MILESTONE_ORDER',
  MILESTONE_SALES = 'MILESTONE_SALES',
  DELIVERY_STATUS = 'DELIVERY_STATUS',
  CONVERSION = 'CONVERSION',
  FUNDS_REVERSED = 'FUNDS_REVERSED',
  STOCK_THRESHOLD = 'STOCK_THRESHOLD',
}

export enum DELIVERY_METHOD {
  WEB_PUSH = 'WEB_PUSH',
  FIREBASE = 'FIREBASE',
}

@Schema({ timestamps: true })
export class PushNotificationSubscription {
  @Prop({ type: String })
  user_id: string;

  @Prop({ type: String })
  endpoint: string;

  @Prop({ type: String })
  public_key: string;

  @Prop({ type: String })
  private_key: string;

  @ApiProperty()
  @Prop({ type: String, default: COUNTRY_CODE.NG })
  country: COUNTRY_CODE;
}

@Schema({ timestamps: true })
export class FirebaseSubscription {
  @Prop({ type: String, required: true })
  @ApiProperty({ description: 'Reference to the user' })
  user: string;

  @Prop({ type: String, required: true })
  @ApiProperty({ description: 'FCM token' })
  fcm_token: string;

  @ApiProperty({ enum: COUNTRY_CODE, default: COUNTRY_CODE.NG })
  @Prop({ type: String, default: COUNTRY_CODE.NG })
  country: COUNTRY_CODE;
}

@Schema({ timestamps: true })
export class Notification {
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  user: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Store', required: false })
  store?: string;

  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String, required: true })
  message: string;

  @Prop({ type: String, required: true })
  path: string;

  @Prop({ type: Boolean, default: false })
  read: boolean;

  @Prop({ type: String, required: true, enum: Object.values(NOTIFICATION_TYPE) })
  type: NOTIFICATION_TYPE;

  @Prop({ type: Object, default: {} })
  data: Record<string, any>;

  @Prop({ type: [String], enum: [DELIVERY_METHOD.WEB_PUSH, DELIVERY_METHOD.FIREBASE], default: [] })
  delivery_methods: DELIVERY_METHOD[];
}

export const PushNotificationSubscriptionSchema = SchemaFactory.createForClass(PushNotificationSubscription);
export const FirebaseSubscriptionSchema = SchemaFactory.createForClass(FirebaseSubscription);
export const NotificationSchema = SchemaFactory.createForClass(Notification);

// Add indexes for better query performance
NotificationSchema.index({ user: 1, createdAt: -1 });
NotificationSchema.index({ user: 1, store: 1, createdAt: -1 });
NotificationSchema.index({ user: 1, read: 1 });
NotificationSchema.index({ user: 1, store: 1, read: 1 });
