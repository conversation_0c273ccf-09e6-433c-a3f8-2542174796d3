// import { MockJwtStrategy } from '../../tools/test-helpers';
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import faker from 'faker';

import { AppModule } from '../../app.module';
import { UserModule } from './user.module';
import { User } from './user.schema';
import { UserService } from './user.service';
import { Transport } from '@nestjs/microservices';
import { LowercasePipe } from '../../pipe/lowercase.pipe';
import { TEST_STORE_ID } from '../../tools/testdata';

describe('UserModule', () => {
  let app: INestApplication;

  let loginToken: string;

  const UserMockObject: Partial<User> = {
    name: faker.name.firstName(),
    email: faker.internet.email(),
    phone: '07033221234',
    password: faker.internet.password(),
  };

  const userExpectedResponse = {
    id: expect.any(String),
    name: expect.any(String),
    email: expect.any(String),
    phone: expect.any(String),
    avatar: expect.any(String),
    email_verified: false,
    phone_verified: false,
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule, UserModule],
    }).compile();

    jest.setTimeout(10000);
    app = module.createNestApplication();
    app.useGlobalPipes(new LowercasePipe(['email']));
    app.connectMicroservice({
      transport: Transport.NATS,
      options: {
        url: process.env.NATS_URL || 'nats://localhost:4222',
      },
    });

    await app.startAllMicroservicesAsync();
    await app.init();
  });

  afterAll(async () => {
    const userService = app.get<UserService>(UserService);
    await userService.deleteUser(UserMockObject.id);
  });

  it('Sign Up user', async () => {
    return await request(app.getHttpServer())
      .post('/users')
      .send(UserMockObject)
      .set('Accept', 'application/json')
      .expect(201)
      .then((res) => {
        expect(res.body.user).toEqual(expect.objectContaining(userExpectedResponse));
        UserMockObject.id = res.body.user.id;
      });
  });

  it('Login user', async () => {
    return await request(app.getHttpServer())
      .post('/users/login')
      .send({
        email: UserMockObject.email,
        password: UserMockObject.password,
      })
      .set('Accept', 'application/json')
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            token: expect.any(String),
            user: expect.objectContaining(userExpectedResponse),
          }),
        );
        loginToken = body.token;
      });
  });

  it('Update Password', async () => {
    return await request(app.getHttpServer())
      .put('/users/password')
      .send({
        current_password: UserMockObject.password,
        new_password: faker.internet.password(),
      })
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${loginToken}`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            message: expect.any(String),
          }),
        );
      });
  });

  it('Request Password Reset', async () => {
    return await request(app.getHttpServer())
      .post('/users/request-password-reset')
      .send({ email: UserMockObject.email })
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${loginToken}`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            message: expect.any(String),
          }),
        );
      });
  });

  it('Update user', async () => {
    const phone = faker.phone.phoneNumber();
    const email = faker.internet.email().toLowerCase();
    const name = faker.name.firstName().toLowerCase();
    return await request(app.getHttpServer())
      .put('/users')
      .send({ phone, name, email })
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${loginToken}`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(
          expect.objectContaining({
            ...userExpectedResponse,
            name,
            email,
            phone,
          }),
        );
        userExpectedResponse.name = name;
        userExpectedResponse.email = email;
        userExpectedResponse.phone = phone;
      });
  });

  it('user profile', async () => {
    return await request(app.getHttpServer())
      .get('/users/me')
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${loginToken}`)
      .expect(200)
      .then(({ body }) => {
        expect(body).toEqual(expect.objectContaining(userExpectedResponse));
      });
  });

  it('refresh user token', async () => {
    return await request(app.getHttpServer())
      .get(`/users/refresh-jwt_token/${TEST_STORE_ID}`)
      .set('Authorization', `Bearer ${loginToken}`)
      .expect(200)
      .then(({ body }) => {
        expect(body.data.token).toBeTruthy();
        expect(body.data).toEqual({ token: expect.any(String) });
      });
  });
});
