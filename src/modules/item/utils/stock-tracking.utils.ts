import { Model, ClientSession } from 'mongoose';
import { Item, ItemDocument } from '../item.schema';
import { Store } from '../../store/store.schema';

export interface StockTrackingResult {
  itemUpdated: boolean;
  variantsUpdated: number;
}

export async function trackStockLevelChanges(
  itemModel: Model<ItemDocument>,
  item: ItemDocument,
  store: Store,
  session?: ClientSession
): Promise<StockTrackingResult> {
  const globalThreshold = store.configuration?.global_stock_threshold || 5;
  const itemThreshold = item.stock_threshold ?? globalThreshold;
  let itemUpdated = false;
  let variantsUpdated = 0;

  if (!store.configuration?.low_stock_notifications_enabled) {
    return { itemUpdated, variantsUpdated };
  }

  if (item.is_always_available) {
    if (item.is_low_stock !== undefined) {
      await itemModel.updateOne(
        { _id: item._id },
        { $unset: { is_low_stock: '' } },
        { session }
      );
      itemUpdated = true;
    }
    return { itemUpdated, variantsUpdated };
  }

  if (item.quantity !== undefined && item.quantity !== null) {
    const shouldBeLowStock = item.quantity <= itemThreshold;
    if (item.is_low_stock !== shouldBeLowStock) {
      await itemModel.updateOne(
        { _id: item._id },
        { $set: { is_low_stock: shouldBeLowStock } },
        { session }
      );
      itemUpdated = true;
    }
  }

  if (item.variants?.options?.length) {
    const variantUpdates: any[] = [];
    
    for (let i = 0; i < item.variants.options.length; i++) {
      const variant = item.variants.options[i];
      
      if (variant.quantity !== undefined && variant.quantity !== null) {
        const shouldBeLowStock = variant.quantity <= itemThreshold;
        
        if (variant.is_low_stock !== shouldBeLowStock) {
          variantUpdates.push({
            updateOne: {
              filter: { 
                _id: item._id,
                [`variants.options.${i}._id`]: variant._id 
              },
              update: { 
                $set: { 
                  [`variants.options.${i}.is_low_stock`]: shouldBeLowStock 
                } 
              },
              session
            }
          });
        }
      }
    }

    if (variantUpdates.length > 0) {
      const result = await itemModel.bulkWrite(variantUpdates, { session });
      variantsUpdated = result.modifiedCount || 0;
    }
  }

  return { itemUpdated, variantsUpdated };
}

export function shouldTrackStock(
  store: Store,
  item: Pick<Item, 'is_always_available' | 'quantity'>
): boolean {
  return (
    store.configuration?.low_stock_notifications_enabled === true &&
    item.is_always_available !== true &&
    item.quantity !== undefined &&
    item.quantity !== null
  );
}

export function calculateLowStockStatus(
  quantity: number,
  itemThreshold?: number,
  globalThreshold?: number
): boolean {
  const threshold = itemThreshold ?? globalThreshold ?? 5;
  return quantity <= threshold;
}
