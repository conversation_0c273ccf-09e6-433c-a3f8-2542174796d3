import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiCreatedResponse, ApiExtraModels, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { IRequest } from '../../../interfaces/request.interface';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import {
  CreateDiscountDto,
  CreateDiscountResponse,
  DiscountResponse,
  DiscountsFilterQueryDto,
  UpdateDiscountDto,
  UpdateDiscountResponse,
} from '../../../models/dtos/ItemDtos';
import { PaginatedQueryDto } from '../custom-items/dtos/get-custom-item';
import { Discount } from './discounts-coupons.schema';
import { ItemService } from '../item.service';
import { DiscountService } from './discounts.service';

@Controller('discounts')
@ApiTags('Discounts')
@ApiExtraModels(Discount)
export class DiscountController {
  constructor(
    private readonly discountService: DiscountService,
    private readonly itemService: ItemService,
    private readonly logger: Logger,
  ) {}

  @Post('')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: CreateDiscountResponse,
  })
  @ApiBody({ type: CreateDiscountDto })
  @ApiSecurity('bearer')
  async createDiscount(@Req() req: IRequest, @Body() discountReq: CreateDiscountDto) {
    try {
      const data = await this.discountService.createDiscount(req, discountReq);
      return {
        message: 'Discount created successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get('')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: DiscountResponse,
  })
  @ApiSecurity('bearer')
  async getDiscount(@Req() req: IRequest, @Query('filter') filter: DiscountsFilterQueryDto, @Query() query: PaginatedQueryDto) {
    try {
      //filter = filter ?? { search: '' };
      const data = await this.discountService.getDiscounts(req.user?.store.id, filter, query);
      return {
        message: 'Discounts retrieved successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Get(':id/items')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: DiscountResponse,
  })
  @ApiSecurity('bearer')
  async getDiscountItems(@Req() req: IRequest, @Param('id') id: string) {
    try {
      const data = await this.discountService.getDiscountItems(id, req.user?.store.id);

      return {
        message: 'Discounts items retrieved successfully',
        data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Put('')
  @UseGuards(JwtAuthGuard)
  @ApiCreatedResponse({
    type: UpdateDiscountResponse,
  })
  @ApiBody({ type: UpdateDiscountDto })
  @ApiSecurity('bearer')
  async updateDiscount(@Req() req: IRequest, @Body() discountReq: UpdateDiscountDto) {
    try {
      const data = await this.discountService.updateDiscount(discountReq, req.user?.store.id);
      return {
        message: 'Discount updated successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteDiscount(@Param('id') id: string) {
    try {
      const data = await this.discountService.deleteDiscount({ id });
      return {
        message: 'Discount deleted successfully',
        data: data,
      };
    } catch (err) {
      this.logger.error(err);
      throw new BadRequestException(err.message);
    }
  }
}
