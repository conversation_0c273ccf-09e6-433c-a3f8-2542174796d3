import mongoose, { FilterQ<PERSON>y, Model, PaginateModel } from 'mongoose';

import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Item, ItemDocument } from '../item.schema';
import {
  CreateDiscountDto,
  DeleteDiscountDto,
  DiscountsFilterQueryDto,
  UpdateDiscountDto,
} from '../../../models/dtos/ItemDtos';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { Discount, DiscountDocument } from './discounts-coupons.schema';
import { convertToObjectIds } from '../../../utils';
import { IRequest } from '../../../interfaces/request.interface';
import computeItemsDiscount from '../utils';
import { DISCOUNT_STATUS_TAG, DISCOUNT_TYPE_TAG } from '../../../enums/item.enum';

@Injectable()
export class DiscountService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Item.name)
    private readonly itemModel: PaginateModel<ItemDocument>,
    @InjectModel(Discount.name)
    private readonly discountModel: PaginateModel<DiscountDocument>,
    private readonly logger: Logger,
  ) {
    this.logger.setContext('item.service.ts');
  }

  async createDiscount(req: IRequest, data: CreateDiscountDto) {
    const user = req.user;
    const { items } = data;
    if (items && items.length > 0) {
      const discount = await this.discountModel.create({
        ...data,
        store: user.store.id,
      });
      const itemData = { discount: discount.id };
      const ids = convertToObjectIds(items as string[]);

      await this.itemModel.updateMany({ _id: { $in: [...ids] } }, itemData);
      return discount;
    }
    throw new BadRequestException('Cannot create discount without item data');
  }

  constructFilterQuery(filterQueryDto: DiscountsFilterQueryDto, storeId: string) {
    const { search, status, type, ...genericFilters } = filterQueryDto;

    const filters: FilterQuery<Discount> = {
      ...genericFilters,
      is_deleted: { $ne: true },
      store: storeId as any,
    };

    const $or: FilterQuery<Discount>['$or'] = [];

    if (search) {
      filters.label = new RegExp(search, 'ig');
    }

    if (status) {
      switch (status) {
        case DISCOUNT_STATUS_TAG.ACTIVE:
          filters.active = true;
          break;
        case DISCOUNT_STATUS_TAG.INACTIVE:
          filters.active = false;
          break;
      }
    }

    if (type) {
      switch (type) {
        case DISCOUNT_TYPE_TAG.HAS_CAP:
          filters.discount_cap = { $exists: true, $gt: 0 };
        case DISCOUNT_TYPE_TAG.NO_CAP:
          $or.push(
            { discount_cap: { $exists: false } },
            { discount_cap: { $eq: null } },
            { discount_cap: { $eq: undefined } },
          );
      }
    }

    if ($or?.length > 0) filters.$or = $or;
    return filters;
  }

  async getDiscounts(store: string, filterQueryDto: DiscountsFilterQueryDto, paginationQuery?: PaginatedQueryDto) {
    const filter = this.constructFilterQuery(filterQueryDto, store);

    if (paginationQuery) {
      const result = await this.discountModel.paginate(filter, {
        page: paginationQuery.page || 1,
        limit: paginationQuery.per_page || 50,
        lean: true,
        sort: { createdAt: paginationQuery?.sort === 'ASC' ? 1 : -1 },
      });

      return {
        items: result.docs,
        page: result.page,
        next_page: result.nextPage,
        prev_page: result.prevPage,
        total: result.totalDocs,
        total_pages: result.totalPages,
        per_page: result.limit,
      };
    }

    return { items: this.discountModel.find({ store }) };
  }

  async getDiscountItems(id: string, store: string) {
    const discount = await this.discountModel.findOne({ _id: id, store });

    if (!discount) {
      throw new NotFoundException('Discount does not exist');
    }

    const items = await this.itemModel
      .find({ _id: { $in: discount.items } }, { images: 1, name: 1, price: 1, thumbnail: 1 })
      .lean();

    return items;
  }

  async updateDiscount(data: UpdateDiscountDto, store: string) {
    const { id: _id, items } = data;
    delete data.id;

    const prevDiscount = await this.discountModel.findOne({ _id, store });
    if (!prevDiscount) throw new BadRequestException('Discount does not exitst');

    if (items && items.length > 0) {
      const updatedDiscount = await this.discountModel.findOneAndUpdate({ _id }, { ...data }, { new: true });
      const prevItemsIds = convertToObjectIds(prevDiscount.items as string[]);
      const currItemsIds = convertToObjectIds(updatedDiscount.items as string[]);
      const prevItemsUpdate = { discount: undefined, discount_price: null };
      const currItemsUpdate = {
        discount: updatedDiscount.id,
        discount_price: null,
      };

      await this.itemModel.updateMany({ _id: { $in: [...prevItemsIds] } }, prevItemsUpdate);
      await this.itemModel.updateMany({ _id: { $in: [...currItemsIds] } }, currItemsUpdate);

      return updatedDiscount;
    }
    return await this.discountModel.findOneAndUpdate({ _id }, { ...data }, { new: true });
  }

  async deleteDiscount(data: DeleteDiscountDto) {
    await this.itemModel.updateMany({ discount: data.id }, { discount: undefined });
    return this.discountModel.findOneAndDelete({
      _id: data.id,
    });
  }
}
