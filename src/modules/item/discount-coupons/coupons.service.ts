import mongoose, { FilterQuery, PaginateModel } from 'mongoose';

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Item, ItemDocument } from '../item.schema';
import {
  CouponsFilterQueryDto,
  CreateCatlogSponsoredCouponsDto,
  CreateCouponDto,
  DeleteCouponDto,
  DiscountsFilterQueryDto,
  UpdateCouponDto,
  VerifyCouponDto,
} from '../../../models/dtos/ItemDtos';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { Coupon, CouponDocument } from './discounts-coupons.schema';
import { IRequest } from '../../../interfaces/request.interface';
import { Store } from '../../store/store.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { CURRENCIES } from '../../country/country.schema';
import { toCurrency } from '../../../utils';
import { RpcException } from '@nestjs/microservices';
import { COUPON_STATUS_TAG, COUPON_TYPE_TAG } from '../../../enums/item.enum';

@Injectable()
export class CouponService {
  constructor(
    private readonly brokerTransport: BrokerTransportService,
    @InjectModel(Item.name)
    private readonly itemModel: PaginateModel<ItemDocument>,
    @InjectModel(Coupon.name)
    private readonly couponModel: PaginateModel<CouponDocument>,
    private readonly logger: Logger,
  ) {
    this.logger.setContext('item.service.ts');
  }

  async createCoupon(storeId: string, data: CreateCouponDto, sponsored_by_catlog: boolean = false) {
    const current = await this.couponModel.findOne({
      coupon_code: data.coupon_code.toUpperCase(),
      store: storeId,
    });

    if (current) {
      throw new BadRequestException('Coupon with code already exists');
    }

    if (data.type === 'fixed' && data.discount_amount) {
      data.percentage = null;
      data.discount_cap = null;

      const coupon = await this.couponModel.create({
        ...data,
        store: storeId,
        sponsored_by_catlog,
      });
      return coupon;
    } else if (data.type === 'percentage' && data.percentage) {
      data.discount_amount = null;
      const coupon = await this.couponModel.create({
        ...data,
        store: storeId,
        sponsored_by_catlog,
      });

      return coupon;
    }
    throw new BadRequestException('cannot create discount without type data');
  }

  async createCatlogSponsoredCoupons(data: CreateCatlogSponsoredCouponsDto) {
    //validate existence of stores provided
    const storesCount = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.STORE.GET_TOTAL, { _id: { $in: data.stores } })
      .toPromise();
    const storesExist = storesCount === data.stores.length;

    if (!storesExist) {
      throw new BadRequestException('One or more of store ids provided are invalid');
    }

    const { stores, ...couponData } = data;

    const coupons = stores.map((store) => {
      return this.createCoupon(store, couponData, true);
    });

    return await Promise.all(coupons);
  }

  constructFilterQuery(filterQueryDto: CouponsFilterQueryDto, storeId: string) {
    const { search, status, type, ...genericFilters } = filterQueryDto;

    const filters: FilterQuery<Coupon> = {
      ...genericFilters,
      is_deleted: { $ne: true },
      store: storeId as any,
    };

    const $or: FilterQuery<Coupon>['$or'] = [];
    const $and: FilterQuery<Coupon>['$and'] = [];

    if (search) {
      filters.coupon_code = new RegExp(search, 'ig');
    }

    if (status) {
      switch (status) {
        case COUPON_STATUS_TAG.ACTIVE:
          filters.active = true;
          break;
        case COUPON_STATUS_TAG.INACTIVE:
          filters.active = false;
          break;
      }
    }

    if (type) {
      switch (type) {
        case COUPON_TYPE_TAG.FIXED:
          filters.type = 'fixed';
          break;
        case COUPON_TYPE_TAG.PERCENTAGE:
          filters.type = 'percentage';
          break;
        case COUPON_TYPE_TAG.HAS_MINIMUM_PURCHASE:
          $and.push(
            { minimum_order_amount: { $gt: 0 } },
            { minimum_order_amount: { $ne: null } },
            { minimum_order_amount: { $ne: undefined } },
          );
          break;
        case COUPON_TYPE_TAG.NO_MINIMUM_PURCHASE:
          $or.push(
            { minimum_order_amount: { $exists: false } },
            { minimum_order_amount: { $eq: null } },
            { minimum_order_amount: { $eq: undefined } },
          );
          break;
      }
    }

    if ($or?.length > 0) filters.$or = $or;
    if ($and?.length > 0) filters.$and = $and;

    return filters;
  }

  async getCoupons(user: IRequest['user'], filterQueryDto: CouponsFilterQueryDto, paginationQuery?: PaginatedQueryDto) {
    const filter = this.constructFilterQuery(filterQueryDto, user.store.id);

    if (paginationQuery) {
      const result = await this.couponModel.paginate(filter, {
        page: paginationQuery.page || 1,
        limit: paginationQuery.per_page || 50,
        lean: true,
        sort: { createdAt: paginationQuery?.sort === 'ASC' ? 1 : -1 },
      });

      return {
        items: result.docs,
        page: result.page,
        next_page: result.nextPage,
        prev_page: result.prevPage,
        total: result.totalDocs,
        total_pages: result.totalPages,
        per_page: result.limit,
      };
    }

    return { items: this.couponModel.find({ store: user.store.id }) };
  }

  async getCouponByCode(user: IRequest['user'], code: string) {
    return this.couponModel.findOne({
      coupon_code: code.toUpperCase(),
      store: user.store.id,
    });
  }

  async trackOrderCoupon(order: {
    order_id: string;
    coupon_code: string;
    shouldCancelOrder?: boolean;
    store: string;
    customer: string;
  }) {
    if (order.shouldCancelOrder === true) {
      return this.couponModel.findOneAndUpdate(
        { coupon_code: order.coupon_code, store: order.store },
        { $inc: { quantity: 1 }, $pull: { orders: order.order_id, used_by: order.customer } },
        { useFindAndModify: false },
      );
    }

    return this.couponModel.findOneAndUpdate(
      { coupon_code: order.coupon_code, store: order.store },
      { $inc: { quantity: -1 }, $push: { orders: order.order_id, used_by: order.customer } },
      { useFindAndModify: false },
    );
  }

  async updateCoupon(user: IRequest['user'], data: UpdateCouponDto) {
    const { id: _id } = data;
    delete data.id;

    const current = await this.couponModel.findOne({ _id });
    if (!current) {
      throw new BadRequestException('Coupon with code already exists');
    }

    if (data.type === 'fixed' && data.discount_amount) {
      data.percentage = null;
      data.discount_cap = null;

      const coupon = await this.couponModel.findOneAndUpdate({ _id }, { ...data, store: user.store.id }, { new: true });

      return coupon;
    } else if (data.type === 'percentage' && data.percentage) {
      data.discount_amount = null;
      const coupon = await this.couponModel.findOneAndUpdate({ _id }, { ...data, store: user.store.id }, { new: true });

      return coupon;
    }

    delete data.percentage;
    delete data.discount_cap;
    delete data.discount_amount;
    delete data.type;

    const coupon = await this.couponModel.findOneAndUpdate({ _id }, { ...data, store: user.store.id }, { new: true });

    return coupon;
  }

  async deleteCoupon(data: DeleteCouponDto) {
    return this.couponModel.findOneAndDelete({
      _id: data.id,
    });
  }

  async verifyCoupon(data: VerifyCouponDto, fromRemote: boolean = false) {
    let errorMessage;
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: data.store })
      .toPromise();

    const exchangeRates = store?.currencies?.rates;

    const coupon = await this.couponModel.findOne({
      coupon_code: data?.coupon_code.toUpperCase(),
      quantity: { $gt: 0 },
      store: data.store,
      end_date: { $gt: new Date() },
      active: true,
    });

    if (!coupon) {
      errorMessage = 'This coupon is invalid or has been used';
      this.throwErrors(errorMessage, fromRemote);
    }

    if (data.customer && coupon.used_by.includes(data.customer.toString() as any)) {
      errorMessage = 'This coupon has already been used by you';
      this.throwErrors(errorMessage, fromRemote);
    }

    const convertCurrency = (amount: number) => this.convertToCurrency(amount, data?.currency, exchangeRates);

    if (coupon.minimum_order_amount && coupon.minimum_order_amount > data.itemsAmount) {
      errorMessage = `To use this coupon, you must make a purchase of at least ${toCurrency(
        convertCurrency(coupon?.minimum_order_amount),
        data?.currency,
      )}`;
      this.throwErrors(errorMessage, fromRemote);
    }

    return coupon;
  }

  throwErrors(message: string, fromRemote?: boolean) {
    if (fromRemote) throw new RpcException({ message });
    throw new BadRequestException(message);
  }

  convertToCurrency(amount: number, currency: CURRENCIES, rates: { [key: string]: number }) {
    const conversionRate = rates && rates[currency] !== undefined ? rates[currency] : 1;

    return Math.ceil((amount / conversionRate) * 100) / 100; //format to two decimal places
  }
}
