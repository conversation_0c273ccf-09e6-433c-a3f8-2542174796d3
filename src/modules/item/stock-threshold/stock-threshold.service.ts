import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Store } from '../../store/store.schema';
import { Item, ItemDocument } from '../item.schema';
import { Order, OrderDocument } from '../../orders/order.schema';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { NOTIFICATION_TYPE } from '../../user/notifications/notification.schema';
import { ResendRepository } from '../../../repositories/resend.repository';
import { isDefined } from 'class-validator';

export interface StockThresholdData {
  store: Store;
  item: Item;
  variantId?: string;
  variantName?: string;
  currentQuantity: number;
  threshold: number;
  orderId: string;
  isOutOfStock?: boolean;
}

@Injectable()
export class StockThresholdService {
  private readonly logger = new Logger(StockThresholdService.name);

  constructor(
    @InjectModel(Item.name) private itemModel: Model<ItemDocument>,
    // @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    private readonly brokerTransportService: BrokerTransportService,
    private readonly resendRepository: ResendRepository,
  ) {}

  async checkStockThreshold(orderId: string): Promise<void> {
    try {
      const order = await this.brokerTransportService
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_LEAN, { _id: orderId })
        .toPromise();

      if (!order || !order.store) {
        this.logger.warn(`Order ${orderId} not found or has no store`);
        return;
      }
      const store = await this.brokerTransportService
        .send<Store>(BROKER_PATTERNS.STORE.GET_STORE_LEAN, { filter: { _id: order.store }, populate: ['owner'] })
        .toPromise();

      const globalThreshold = store.configuration.global_stock_threshold || 5;

      for (const orderItem of order.items) {
        const item = await this.itemModel.findById(orderItem.item_id).exec();

        if (!item) {
          this.logger.warn(`Item ${orderItem.item_id} not found`);
          continue;
        }

        if (item.is_always_available || !isDefined(item.quantity)) {
          continue;
        }

        // Item-specific threshold takes priority over global threshold
        const itemThreshold = item.stock_threshold !== undefined ? item.stock_threshold : globalThreshold;

        if (orderItem.variant_id) {
          // Check variant stock
          await this.checkVariantStock(store, item, orderItem.variant_id, itemThreshold, orderId);
        } else {
          // Check main item stock
          await this.checkMainItemStock(store, item, itemThreshold, orderId);
        }
      }
    } catch (error) {
      this.logger.error(`Error checking stock threshold for order ${orderId}:`, error);
    }
  }

  private async checkVariantStock(
    store: Store,
    item: Item,
    variantId: string,
    threshold: number,
    orderId: string,
  ): Promise<void> {
    const variant = item.variants?.options?.find((option) => option._id?.toString() === variantId?.toString());

    if (!variant) {
      this.logger.warn(`Variant ${variantId} not found for item ${item._id}`);
      return;
    }

    if (!isDefined(variant.quantity)) {
      return;
    }

    const variantQuantity = variant.quantity || 0;
    const isOutOfStock = variantQuantity === 0;
    const isBelowThreshold = variantQuantity <= threshold && variantQuantity > 0;

    if (isOutOfStock || isBelowThreshold) {
      const thresholdData: StockThresholdData = {
        store,
        item,
        variantId: variant._id?.toString(),
        variantName: Object.entries(variant.values || {})
          .map(([key, value]) => `${key}: ${value}`)
          .join(', '),
        currentQuantity: variantQuantity,
        threshold,
        orderId,
        isOutOfStock,
      };

      await this.sendStockThresholdNotifications(thresholdData);
    }
  }

  private async checkMainItemStock(store: Store, item: Item, threshold: number, orderId: string): Promise<void> {
    const itemQuantity = item.quantity || 0;
    const isOutOfStock = itemQuantity === 0;
    const isBelowThreshold = itemQuantity <= threshold && itemQuantity > 0;

    if (isOutOfStock || isBelowThreshold) {
      const thresholdData: StockThresholdData = {
        store,
        item,
        currentQuantity: itemQuantity,
        threshold,
        orderId,
        isOutOfStock,
      };

      await this.sendStockThresholdNotifications(thresholdData);
    }
  }

  private async sendStockThresholdNotifications(data: StockThresholdData): Promise<void> {
    try {
      // Send email notification

      this.logger.log(`Processing stock threshold notification for item ${data.item.name}`);

      // Get store owner information
      const store = data.store;
      const owner = store.owner;

      if (!owner || typeof owner === 'string') {
        this.logger.warn(`Store owner not populated for store ${store._id}`);
        return;
      }

      const itemDisplayName = data.variantId ? `${data.item.name} (${data.variantName})` : data.item.name;

      const alertType = data.isOutOfStock ? 'Out of Stock' : 'Low Stock';
      const subject = `${alertType} Alert: ${itemDisplayName} - ${store.name}`;

      const emailData = {
        preview_text: `${alertType} alert for ${itemDisplayName} in your store ${store.name}`,
        store_name: store.name,
        owner_name: owner.name || owner.email,
        item_name: data.item.name,
        variant_name: data.variantName,
        current_quantity: data.currentQuantity,
        threshold: data.threshold,
        is_out_of_stock: data.isOutOfStock || false,
        dashboard_link: `${process.env.CATLOG_WWW}`,
      };

      await this.resendRepository.sendEmail(BROKER_PATTERNS.MAIL.STOCK_THRESHOLD_NOTIFICATION, {
        to: owner.email,
        subject,
        data: emailData,
      });

      this.logger.log(`Stock threshold notification email sent to ${owner.email}`);

      // Send push notification
      const notificationTitle = data.isOutOfStock ? 'Out of Stock Alert' : 'Low Stock Alert';
      const itemName = data.variantId ? `${data.item.name} (${data.variantName})` : data.item.name;

      const notificationBody = data.isOutOfStock
        ? `${itemName} is now out of stock`
        : `${itemName} is running low on stock (${data.currentQuantity} remaining)`;

      this.brokerTransportService.emit(BROKER_PATTERNS.USER.SEND_PUSH_NOTIFICATION, {
        store: data.store._id,
        owner_only: true,
        message: {
          title: notificationTitle,
          message: notificationBody,
          path: '/dashboard/items',
        },
        notification_type: NOTIFICATION_TYPE.STOCK_THRESHOLD,
        data: {
          type: 'stock_threshold',
          itemId: data.item._id,
          storeId: data.store._id,
          variantId: data.variantId,
          orderId: data.orderId,
          isOutOfStock: data.isOutOfStock,
          currentQuantity: data.currentQuantity,
          threshold: data.threshold,
        },
      });

      this.logger.log(`Stock threshold notifications sent for ${itemName} in store ${data.store.name}`);
    } catch (error) {
      this.logger.error('Error sending stock threshold notifications:', error);
    }
  }
}
