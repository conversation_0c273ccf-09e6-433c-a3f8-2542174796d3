import { BullModule } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import mongoosePaginate from 'mongoose-paginate-v2';
import { QUEUES } from '../../enums/queues.enum';
import jsonHookPlugin from '../../mongoose-plugins/json-hook.mongoose-plugin';
import setIdPlugin from '../../mongoose-plugins/set-id.mongoose-plugin';
import trackFieldsHistoryPlugin from '../../mongoose-plugins/track-fields-history-plugin';
import { SharedModule } from '../../shared.module';
import { InfoBlock, InfoBlockSchema } from '../store/info-blocks.schema';
import { StoreModule } from '../store/store.module';
import { CustomItemController } from './custom-items/custom-item.controller';
import { CustomItem, CustomItemSchema } from './custom-items/custom-item.schema';
import { CustomItemService } from './custom-items/custom-item.service';
import { CouponsController } from './discount-coupons/coupons.controller';
import { CouponService } from './discount-coupons/coupons.service';
import { Coupon, CouponSchema, Discount, DiscountSchema } from './discount-coupons/discounts-coupons.schema';
import { DiscountController } from './discount-coupons/discounts.controller';
import { DiscountService } from './discount-coupons/discounts.service';
import { HighlightsController } from './highlights/highlights.controller';
import { Highlight, HighlightSchema } from './highlights/highlights.schema';
import { HighlightsService } from './highlights/highlights.service';
import { ItemExportImportController } from './item-export-import.controller';
import { ItemExportImportService } from './item-export-import.service';
import { ItemInfoBlocksService } from './item-info-blocks.service';
import { ItemBroker } from './item.broker';
import { ItemController } from './item.controller';
import { ItemQueue } from './item.queue';
import { Item, ItemSchema } from './item.schema';
import { ItemService } from './item.service';
import { StockThresholdService } from './stock-threshold/stock-threshold.service';
import { TieredPricingController } from './tiered-pricing/tiered-pricing.controller';
import { TieredPricing, TieredPricingSchema } from './tiered-pricing/tiered-pricing.schema';
import { TieredPricingService } from './tiered-pricing/tiered-pricing.service';


@Module({
  imports: [
    BullModule.registerQueue({
      name: QUEUES.ITEM,
    }),
    MongooseModule.forFeatureAsync([
      {
        name: Item.name,
        useFactory: () => {
          ItemSchema.plugin(setIdPlugin());
          ItemSchema.plugin(jsonHookPlugin(['_id', '__v']));
          ItemSchema.plugin(trackFieldsHistoryPlugin(['quantity', 'variants']));
          ItemSchema.plugin(mongoosePaginate);
          return ItemSchema;
        },
      },
      {
        name: Discount.name,
        useFactory: () => {
          DiscountSchema.plugin(setIdPlugin());
          DiscountSchema.plugin(jsonHookPlugin(['_id', '__v']));
          DiscountSchema.plugin(mongoosePaginate);
          return DiscountSchema;
        },
      },
      {
        name: Coupon.name,
        useFactory: () => {
          CouponSchema.plugin(setIdPlugin());
          CouponSchema.plugin(jsonHookPlugin(['_id', '__v']));
          CouponSchema.plugin(mongoosePaginate);
          return CouponSchema;
        },
      },
      {
        name: CustomItem.name,
        useFactory: () => {
          CustomItemSchema.plugin(setIdPlugin());
          CustomItemSchema.plugin(jsonHookPlugin(['_id', '__v']));
          CustomItemSchema.plugin(mongoosePaginate);
          return CustomItemSchema;
        },
      },
      {
        name: InfoBlock.name,
        useFactory: () => {
          InfoBlockSchema.plugin(setIdPlugin());
          InfoBlockSchema.plugin(jsonHookPlugin(['_id', '__v']));
          return InfoBlockSchema;
        },
      },
      {
        name: Highlight.name,
        useFactory: () => {
          HighlightSchema.plugin(setIdPlugin());
          HighlightSchema.plugin(jsonHookPlugin(['_id', '__v']));
          HighlightSchema.plugin(mongoosePaginate);
          return HighlightSchema;
        },
      },
      {
        name: TieredPricing.name,
        useFactory: () => {
          TieredPricingSchema.plugin(setIdPlugin());
          TieredPricingSchema.plugin(jsonHookPlugin(['_id', '__v']));
          TieredPricingSchema.plugin(mongoosePaginate);
          return TieredPricingSchema;
        },
      },
    ]),
    SharedModule,
    forwardRef(() => StoreModule),
  ],

  providers: [
    ItemService,
    CustomItemService,
    DiscountService,
    CouponService,
    ItemQueue,
    ItemExportImportService,
    // ItemRecommendationsService,
    ItemInfoBlocksService,
    HighlightsService,
    TieredPricingService,
    StockThresholdService
  ],
  controllers: [
    ItemController,
    CouponsController,
    DiscountController,
    ItemBroker,
    CustomItemController,
    HighlightsController,
    ItemExportImportController,
    TieredPricingController,
  ],
  exports: [
    ItemService,
    CustomItemService,
    DiscountService,
    CouponService,
    ItemExportImportService,
    // ItemRecommendationsService,
    ItemInfoBlocksService,
    HighlightsService,
    TieredPricingService,
     StockThresholdService
  ],
})
export class ItemModule {}
