import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsDateString, IsEmail, IsEnum, IsOptional, IsString } from 'class-validator';
import { INVOICE_STATUSES, INVOICE_TYPE } from '../invoice.schema';
import { INVOICES_DRAFT_TAG, INVOICES_PAID_TAG } from '../../../enums/invoices.enum';
import { CURRENCIES } from '../../country/country.schema';


export class PaginatedQueryDto {
  @ApiProperty({ required: false })
  page: number;

  @ApiProperty({ required: false })
  per_page: number;

  @ApiProperty({ required: false, type: 'string', enum: ['ASC', 'DESC'] })
  sort?: any;
}

export class InvoiceEmailDto {
  @ApiProperty({ required: true })
  @IsEmail()
  email: string;

  @ApiProperty({ required: true })
  @IsString()
  message: string;
}

export class InvoicesFilterQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty({ required: false })
  search?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  store?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  to?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(INVOICE_STATUSES)
  status?: INVOICE_STATUSES;


  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(INVOICE_TYPE)
  type?: INVOICE_TYPE;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(INVOICES_DRAFT_TAG)
  draft_status?: INVOICES_DRAFT_TAG;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(CURRENCIES)
  currency?: CURRENCIES;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  products?: string[];
}
