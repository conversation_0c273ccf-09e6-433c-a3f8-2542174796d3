import {
  Body,
  Controller,
  Get,
  Header,
  HttpException,
  HttpStatus,
  UseGuards,
  BadRequestException,
  Param,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBody, ApiConsumes, ApiOperation, ApiSecurity } from '@nestjs/swagger';
import { UtilsService } from './utils.service';
import { IRequest, IResponse } from '../../interfaces/request.interface';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { ImageDto, MenuImageDto } from '../../models/dtos/file.dto';
import { PlanPermissions } from '../../decorators/permission.decorator';
import { SCOPES } from '../../utils/permissions.util';
import { PlanGuard } from '../../guards/plan.guard';
import { validateSitemapToken } from '../whatsapp-bot/utils/functions.utils';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('utils')
export class UtilsController {
  constructor(private readonly utilsService: UtilsService) {}

  @Get('get-metatags-from-page')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async getMetatagsFromPage(@Req() req: IRequest, @Query('username') username: string) {
    const data = await this.utilsService.getInstagramMetatags(username);

    return {
      message: 'Fetched metatags',
      data,
    };
  }

  @Get('generate-manifest-csv-file')
  async generateManifestFile(@Req() req: IRequest, @Res() res: IResponse) {
    const data = await this.utilsService.generateManifestFile();
    res.setHeader('Content-Type', 'text/csv');
    res.send(data);
    return {};
  }

  @Post('extract-menu-items')
  @PlanPermissions(SCOPES.PLAN_PERMISSIONS.CAN_EXTRACT_ITEMS_FROM_MENU_IMAGE)
  @UseGuards(JwtAuthGuard, PlanGuard)
  @ApiSecurity('bearer')
  async extractItemsFromMenuImage(@Req() req: IRequest, @Body() body: MenuImageDto) {
    const data = await this.utilsService.extractItemsFromMenuImage(body);
    return {
      message: 'Extracted Successfully',
      data,
    };
  }

  @Post('extract-colors')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Upload a Image to Extract Image Color',
    type: ImageDto,
  })
  @UseInterceptors(FileInterceptor('image'))
  async extractColors(@UploadedFile() file: Express.Multer.File) {
    try {
      if (!file) {
        throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
      }

      // Call the utils service method
      const colors = await this.utilsService.extractColors(file.buffer);

      return {
        message: 'Successfully extracted colors',
        data: colors,
      };
    } catch (error) {
      console.error('Error extracting colors:', error);
      throw new HttpException('Failed to extract colors', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('get-product-details-from-caption')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async getProductDetailsFromCaption(@Req() req: IRequest, @Body() body: { caption: string }) {
    const data = await this.utilsService.getProductDetailsFromCaption(decodeURIComponent(body.caption));
    return {
      message: 'Successfully generated product details',
      data,
    };
  }

  @Post('get-product-details-from-multiple-captions')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async getProductDetailsFromMultipleCaptions(
    @Req() req: IRequest,
    @Body() body: { captions: { caption: string; key: string }[] },
  ) {
    const data = await this.utilsService.getProductDetailsFromMultipleCaptions(body.captions);

    return {
      message: 'Successfully generated product details',
      data,
    };
  }

  @Get('sitemaps/get-pages')
  async getSitemapPages() {
    const data = await this.utilsService.getSitemapPages();

    return {
      message: 'Fetched sitemap pages',
      data,
    };
  }

  // @Get('sitemaps/get-pages')
  // async getSitemapPages() {
  //   const data = await this.utilsService.getSitemapPages();

  //   return {
  //     message: 'Fetched sitemap pages',
  //     data,
  //   };
  // }

  @Get('sitemaps/items')
  async getSitemapItems(@Query('page') page: number, @Query('token') token: string) {
    validateSitemapToken(token);
    const data = await this.utilsService.getSitemapItems(page);

    return {
      message: 'Fetched sitemap items',
      data,
    };
  }

  @Get('sitemaps/stores')
  async getSitemapStores(@Query('page') page: number, @Query('token') token: string) {
    validateSitemapToken(token);
    const data = await this.utilsService.getSitemapStores(page);

    return {
      message: 'Fetched sitemap stores',
      data,
    };
  }

  @Get('sitemaps.xml')
  async getMainSitemap(@Query('token') token: string, @Res() res: IResponse) {
    validateSitemapToken(token);
    const sitemap = await this.utilsService.generateMainSitemap();
    res.setHeader('Content-Type', 'text/xml');
    res.send(sitemap);

    return {};
  }

  // @Get('sitemaps/stores/:page/map.xml')
  // async getStoreSitemap(@Param('page') page: number, @Res() res: IResponse, @Query('token') token: string) {
  //   validateSitemapToken(token);
  //   const sitemap = await this.utilsService.generateStoreSitemap(page);
  //   res.setHeader('Content-Type', 'text/xml');
  //   res.send(sitemap);
  //   return {};
  // }

  // @Get('sitemaps/products/:page/map.xml')
  // async getProductSitemap(@Param('page') page: number, @Res() res: IResponse, @Query('token') token: string) {
  //   validateSitemapToken(token);
  //   const sitemap = await this.utilsService.generateProductSitemap(page);
  //   res.setHeader('Content-Type', 'text/xml');
  //   res.send(sitemap);
  //   return {};
  // }

  // @Get('sitemaps/stores/:page')
  // async getStoreSitemap(@Param('page') page: number, @Res() res: IResponse) {
  //   const sitemap = await this.utilsService.generateStoreSitemap(page);
  //   res.setHeader('Content-Type', 'text/xml');
  //   res.send(sitemap);
  //   return {};
  // }

  // @Get('sitemaps/products/:page')
  // async getProductSitemap(@Param('page') page: number, @Res() res: IResponse) {
  //   const sitemap = await this.utilsService.generateProductSitemap(page);
  //   res.setHeader('Content-Type', 'text/xml');
  //   res.send(sitemap);
  //   return {};
  // }

  @Post('images-to-strings')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async convertImagesToStrings(@Body() urls: { images: string[] }) {
    const data = await this.utilsService.convertImageUrlsToStrings(urls.images);
    return {
      message: 'Converted images successfully',
      data,
    };
  }

  @Get('get-wrapped-screenshots/:store_slug')
  async captureScreenshots(@Query('cards') cards: string, @Param('store_slug') store_slug: string) {
    const cardsArray = cards.split(','); // Receive URLs as query params

    const data = await this.utilsService.getWrappedScreenshots(cardsArray, store_slug);

    return {
      message: 'Screenshots captured successfully',
      data,
    };
  }

  @Get('download-file')
  @ApiSecurity('bearer')
  @ApiOperation({ summary: 'Download a file from a URL' })
  async downloadFile(@Query('url') url: string, @Res() res: IResponse) {
    const decodedUrl = decodeURIComponent(escape(atob(url)));
    if (!url) {
      throw new BadRequestException('URL is required');
    }

    const file = await this.utilsService.downloadFile(decodedUrl);

    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${file.filename}"`);
    res.send(file.buffer);

    return {};
  }

  @Get('file-info')
  @ApiSecurity('bearer')
  @ApiOperation({ summary: 'Get file information from URL' })
  async getFileInfo(@Query('url') url: string) {
    const decodedUrl = decodeURIComponent(escape(atob(url)));
    if (!url) {
      throw new BadRequestException('URL is required');
    }

    const fileInfo = await this.utilsService.getUrlHeadInfo(decodedUrl);
    return {
      message: 'File information retrieved successfully',
      data: fileInfo,
    };
  }

  @Get('mobile-app-config')
  @ApiOperation({ summary: 'Get mobile app feature flags and required version' })
  async getMobileAppConfig() {
    const data = await this.utilsService.getMobileAppConfig();

    return {
      message: 'Mobile app configuration fetched successfully',
      data,
    };
  }
}
