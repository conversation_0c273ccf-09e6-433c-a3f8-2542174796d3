import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiOkResponse, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { RolePermissions } from '../../../decorators/permission.decorator';
import { RoleGuard } from '../../../guards/role.guard';
import { IRequest } from '../../../interfaces/request.interface';
import { JwtAuthGuard } from '../../../jwt/jwt-auth.guard';
import { PaginatedQueryDto } from '../../../models/dtos/PaginatedDto';
import { SCOPES } from '../../../utils/permissions.util';
import { CreateCustomerDto, CustomerFilterDto, UpdateCustomerDto } from '../dto/create-order.dto';
import { Customer } from './customer.schema';
import { CustomerService } from './customer.service';
import { CustomersFilterQueryDto } from '../../../models/dtos/order.dto';
import { FilterQuery } from 'mongoose';

@ApiTags('Customer')
@Controller('customers')
export class CustomerController {
  constructor(private readonly customerService: CustomerService) {}

  @Post('public')
  @ApiOkResponse({ type: Customer })
  async createPublicCustomer(@Req() req: IRequest, @Body() body: CreateCustomerDto) {
    const data = await this.customerService.createCustomer(body, true);
    return {
      message: 'Customers created successfully',
      data,
    };
  }

  @Post('')
  @RolePermissions(SCOPES.CUSTOMERS.UPDATE_CUSTOMERS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Customer })
  async createCustomers(@Req() req: IRequest, @Body() body: CreateCustomerDto) {
    // checkIfUserOwnsStore(req.user.store as Store, body.store)
    body.store = req.user.store.id;

    const data = await this.customerService.createCustomer(body);
    return {
      message: 'Customers created successfully',
      data,
    };
  }

  @Get('statistics')
  @ApiSecurity('bearer')
  @UseGuards(JwtAuthGuard)
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
          description: 'Start date for the statistics (defaults to current date)',
        },
        to: {
          type: 'string',
          format: 'date',
          description: 'End date for the statistics (defaults to current date)',
        },
      },
    },
    required: false,
  })
  async getCustomerStatistics(@Req() req: IRequest, @Query('filter') filter: any) {
    const storeId = req.user.store.id;
    const data = await this.customerService.getCustomerStatistics(storeId, filter);

    return {
      message: 'Customer statistics fetched successfully',
      data,
    };
  }

  @Get('')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Customer })
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
        search: {
          type: 'string',
        },
      },
    },
    required: false,
  })

  async getPaginatedCustomers(
    @Req() req: IRequest,
    @Query() query: PaginatedQueryDto,
    @Query('filter') filter: CustomersFilterQueryDto,
  ) {
    filter.store = req.user.store.id;
    const data = await this.customerService.getPaginatedCustomers(filter, query);
    return {
      message: 'Customers paginated successfully',
      data,
    };
  }

  @Get('basic')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Customer })
  @ApiQuery({
    name: 'filter',
    schema: {
      type: 'object',
      properties: {
        from: {
          type: 'string',
          format: 'date',
        },
        to: {
          type: 'string',
          format: 'date',
        },
        search: {
          type: 'string',
        },
      },
    },
    required: false,
  })
  async getPaginatedCustomersBasic(
    @Req() req: IRequest,
    @Query() query: PaginatedQueryDto,
    @Query('filter') filter: CustomersFilterQueryDto,
  ) {
    filter.store = req.user.store.id;
    const data = await this.customerService.getPaginatedCustomersBasic(filter, query);
    return {
      message: 'Customers fetched successfully',
      data,
    };
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Customer })
  async getCustomer(@Req() req: IRequest, @Param('id') customerId: string) {
    const storeId = req.user.store.id;
    const data = await this.customerService.getCustomer(storeId, customerId);

    return {
      message: 'Customer fetched successfully',
      data,
    };
  }

  @Put(':id')
  @RolePermissions(SCOPES.CUSTOMERS.UPDATE_CUSTOMERS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiSecurity('bearer')
  @ApiOkResponse({ type: Customer })
  async updateCustomer(@Req() req: IRequest, @Param('id') customerId: string, @Body() update: UpdateCustomerDto) {
    delete update.store;
    const storeId = req.user.store.id;
    const data = await this.customerService.updateCustomer(storeId, customerId, update);

    return {
      message: 'Customer updated successfully',
      data,
    };
  }

  @Put(':id/public')
  @ApiOkResponse({ type: Customer })
  async publicCustomerUpdate(@Req() req: IRequest, @Param('id') customerId: string, @Body() update: UpdateCustomerDto) {
    const data = await this.customerService.publicCustomerUpdate(customerId, update);

    return {
      message: 'Information updated successfully',
      data,
    };
  }

  @Delete(':id')
  @RolePermissions(SCOPES.CUSTOMERS.UPDATE_CUSTOMERS)
  @UseGuards(JwtAuthGuard, RoleGuard)
  @ApiSecurity('bearer')
  async deleteCustomer(@Req() req: IRequest, @Param('id') id: string) {
    const filter = {
      _id: id,
      store: req.user.store.id,
    };
    await this.customerService.deleteCustomer(filter);
    return {
      message: 'Customer deleted successfully',
    };
  }
}
