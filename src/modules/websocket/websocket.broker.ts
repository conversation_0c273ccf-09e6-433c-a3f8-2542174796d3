import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BROKER_PATTERNS } from '../../enums/broker.enum';
import { bridge } from './event.bridge';
import { SkipThrottle } from '@nestjs/throttler';

@SkipThrottle()
@Controller()
export class WebsocketGatewayBroker {
  constructor() {}

  @MessagePattern(BROKER_PATTERNS.WEBSOCKET.TARGETTED_MESSAGE)
  async sendTargettedMessage(data: { id: string; data: any }) {
    bridge.emit('data', data);
  }
}
