import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Post,
  Put,
  Query,
  Req,
  ServiceUnavailableException,
  UseGuards,
} from '@nestjs/common';
import { DeliveryService } from './deliveries.service';
import { SCOPES } from '../../utils/permissions.util';
import { JwtAuthGuard } from '../../jwt/jwt-auth.guard';
import { ApiExcludeEndpoint, ApiSecurity } from '@nestjs/swagger';
import {
  CreateAddressDto,
  CreateDeliveryDraftDto,
  InitiateDeliveryDto,
  UpdateDeliveryDraftDto,
} from '../../models/dtos/delivery.dto';
import { IRequest } from '../../interfaces/request.interface';
import { PaginatedQueryDto } from '../../models/dtos/PaginatedDto';
import { Store } from '../store/store.schema';
import { CountryGuard } from '../../guards/country.guard';
import { PlanGuard } from '../../guards/plan.guard';
import { CountryPermissons, PlanPermissions } from '../../decorators/permission.decorator';
import COURIER_LOGOS from '../../utils/courier-logos';
import { InternalApiJWTGuard } from '../../guards/api.guard';
import { DELIVERY_STATUS } from '../../enums/deliveries';

@Controller('deliveries')
export class DeliveriesController {
  constructor(private readonly deliveryService: DeliveryService, private readonly logger: Logger) {}

  @Get('/package-categories')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getPackageCategories() {
    const data = await this.deliveryService.getPackageCategories();
    return {
      data,
      message: 'Package categories fetched successfully',
    };
  }

  @Get('/statistics')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getDeliveryStatistics(@Req() request: IRequest) {
    const data = await this.deliveryService.getStatistics(request.user.store.id);
    return {
      data,
      message: 'Delivery Statistics fetched successfully',
    };
  }

  @Get('/couriers')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getCouriers(@Req() request: IRequest) {
    const data = await this.deliveryService.shipbubble.getCouriers();

    if (data.error) {
      throw new ServiceUnavailableException("Couldn't fetch couriers from third-party");
    }

    const availableCouriers = [];

    for (let index = 0; index < data.data.length; index++) {
      const courier = data.data[index];

      if (courier.status === 'operational') {
        availableCouriers.push({ ...courier, courier_image: COURIER_LOGOS[courier.service_code] ?? courier.pin_image });
      }
    }

    return {
      data: availableCouriers,
      message: 'Delivery Couriers',
    };
  }

  @Get('/package-dimensions')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getPackageDimensions() {
    const data = await this.deliveryService.getPackageDimensions();
    return {
      data,
      message: 'Package Dimensions fetched successfully',
    };
  }

  @Post('/addresses')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async createAddress(@Req() request: IRequest, @Body() addressReq: CreateAddressDto) {
    const data = await this.deliveryService.createAddressUsingGoogleLookup(request.user.store.id, addressReq);
    return {
      data,
      message: 'Address created successfully',
    };
  }

  @Post('/')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async saveDeliveryDraft(@Req() request: IRequest, @Body() deliveryReq: CreateDeliveryDraftDto) {
    const data = await this.deliveryService.createDeliveryDraft(request.user.store as Store, deliveryReq);
    return {
      data,
      message: 'Delivery draft created successfully',
    };
  }
  @Put('/:id')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async updateDeliveryDraft(
    @Req() request: IRequest,
    @Param('id') delivery_id: string,
    @Body() deliveryReq: UpdateDeliveryDraftDto,
  ) {
    const data = await this.deliveryService.updateDeliveryDraft(request.user.store.id, deliveryReq, delivery_id);
    return {
      data,
      message: 'Delivery draft updated successfully',
    };
  }

  @Put('/:id/notify-customers')
  @UseGuards(JwtAuthGuard)
  @ApiSecurity('bearer')
  async toggleNotifyCustomers(
    @Req() request: IRequest,
    @Param('id') delivery_id: string,
    @Body() deliveryReq: { state: boolean },
  ) {
    const data = await this.deliveryService.toggleNotifyCustomers(
      request.user.store.id,
      delivery_id,
      deliveryReq.state,
    );
    return {
      data,
      message: 'Delivery updated successfully',
    };
  }

  @Post('/:id/cancel')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async cancelDelivery(@Req() request: IRequest, @Param('id') delivery_id: string) {
    await this.deliveryService.cancelDelivery(request.user.store.id, delivery_id);
    return {
      message: 'Delivery has been cancelled successfully',
    };
  }

  @Get('/:id/shipping-rates')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getShippingRates(@Req() request: IRequest, @Param('id') delivery_id: string) {
    const data = await this.deliveryService.getShippingRates(request.user.store.id, delivery_id);
    return {
      data,
      message: 'Successfully fetched shipping rates',
    };
  }

  @Put('/:id/retry')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async initiateDelivery(@Req() request: IRequest, @Param('id') deliveryId: string) {
    const data = await this.deliveryService.retryDeliveryInitiation(request.user.store.id, deliveryId);

    return {
      data,
      message: 'Delivery initiated successfully',
    };
  }

  @Get('/addresses')
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getStoreAddresses(@Req() request: IRequest, @Query('include_customers') include_customers: boolean) {
    const data = await this.deliveryService.getStoreAddresses(request.user.store.id, include_customers);
    return {
      data,
      message: 'Store addresses fetched successfully',
    };
  }

  @Get('/addresses/:id')
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getAddress(@Req() request: IRequest, @Param('id') address_id: string) {
    const data = await this.deliveryService.getAddress(address_id);
    return {
      data,
      message: 'Address fetched successfully',
    };
  }

  @Get('')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getPaginatedDeliveries(@Req() request: IRequest, @Query() query: PaginatedQueryDto) {
    const { filter, ...pagination } = query;
    const data = await this.deliveryService.getPaginatedDeliveries(request.user.store.id, pagination, filter);

    return {
      data,
      message: 'Store deliveries fetched successfully',
    };
  }
  @Get(':id')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async getDeliveryDetails(@Req() request: IRequest, @Param('id') delivery_id: string) {
    const data = await this.deliveryService.getDelivery(request.user.store.id, delivery_id);
    return {
      data,
      message: 'Fetched delivery successfully',
    };
  }

  @Delete(':id')
  @CountryPermissons(SCOPES.COUNTRY_PERMISSIONS.CAN_BOOK_DELIVERIES)
  @PlanPermissions()
  @UseGuards(JwtAuthGuard, CountryGuard, PlanGuard)
  @ApiSecurity('bearer')
  async deleteDeliveryDraft(@Req() request: IRequest, @Param('id') delivery_id: string) {
    const data = await this.deliveryService.deleteDeliveryDraft(request.user.store.id, delivery_id);
    return {
      data,
      message: 'Deleted delivery draft successfully',
    };
  }

  @Put(':tracking_code/update-status')
  @ApiExcludeEndpoint()
  @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async updateDeliveryStatus(
    @Req() request: IRequest,
    @Param('tracking_code') tracking_code: string,
    @Body() body: { status: DELIVERY_STATUS },
  ) {
    const data = this.deliveryService.updateStatus({
      filter: { tracking_code },
      update: { status: (body.status as string).toUpperCase() },
    });

    return {
      data,
      message: 'Deleted delivery draft successfully',
    };
  }

  @Get('/chowdeck/login')
  @ApiExcludeEndpoint()
  // @UseGuards(JwtAuthGuard, InternalApiJWTGuard)
  async loginToChowdeckDashboard() {
    const data = await this.deliveryService.chowdeck.loginToDashboard();
    return{
      data

    }
  }
}
