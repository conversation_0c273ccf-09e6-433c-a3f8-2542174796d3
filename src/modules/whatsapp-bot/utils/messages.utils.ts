import { DELIVERY_STATUS } from '../../../enums/deliveries';
import { formatText, capitalizeFirstLetter } from '../../../utils';
import { ORDER_STATUSES } from '../../orders/order.schema';
import { conditionallyAppendWhiteSpace, getFirstName } from './functions.utils';

export enum USER_COMMANDS {
  END = 'END',
  MENU = 'MENU',
  HELP = 'HELP',
  GO_BACK = 'BACK',
  SWITCH = 'SWITCH',
  RESEND = 'RESEND',
  SEARCH = 'SEARCH',
  MAIN = 'MAIN',
  RESTART = 'RESTART',
}

export enum CTA_REPLIES {
  PLACE_NEW_ORDER = 'Place an Order',
  REPEAT_ORDER = 'Repeat an Order',
  CONTACT_RESTAURANT = 'Chat with Support',
  CONTACT_SUPPORT = 'Contact Support',
  GO_BACK = 'Go Back',
  RESUME_PROCESS = 'Try Again',
  START_NEW_PROCESS = 'Start New Process',
  RESTART_PROCESS = 'Restart',
  RESTART_NEW_ORDER = 'Place a New Order',
  SWITCH_STORES = 'Switch Stores',
  CONTINUE_PROCESS = 'Continue',
  PROCEED = 'Yes Continue',
  PROCEED_TO_PAYMENT = 'Proceed to Payment',
  SEARCH_FOR_A_STORE = 'Search For a Store',
  FIND_ANOTHER_STORE = 'Find another Store',
  END_PROCESS = 'End Chat',
  ORDER_PREVIOUS_STORE = 'Pick Last Store',
  LOAD_MORE_STORES = 'Load more',
  MAKE_NEW_SEARCH = 'Make new search',
  ENTER_NAME = 'Enter your name',
  EDIT_SELECTIONS = 'Make Changes',
  EDIT_INFO = 'Edit delivery info',
  NEW_INFO = 'New delivery info',
  CONFIRM_SELECTIONS = 'Confirm selections',
  PICKUP = 'Pickup',
  DELIVERY = 'Delivery',
  RESEND_LAST_MESSAGE = 'Send last message',
  MAIN_OPTIONS = 'Go to main options',
  BANK_TRANSFER = 'Bank Transfer',
  OTHER_METHODS = 'Use Other Methods',
  LAST_DELIVERY_INFO = 'Use Last Info',
  NEW_DELIVERY_INFO = 'Use New Info',
  MADE_TRANSFER = "I've transferred",
  SEARCH_STORE_RESULTS_BODY = 'Click here to select from results',
  SEARCH_STORE_RESULTS_CTA = 'See Results',
  SELECT_PREVIOUS_ORDER_CTA = 'Select an Order',
  SELECT_BRANCH_CTA = 'Switch Branches',
  SELECT_PREVIOUS_ORDER_HEADER = 'Previous Orders',
  REJECT_ORDER = 'Decline',
  CONFIRM_ORDER = 'Confirm',
  RETRY_CONFIRMATION = 'Retry Confirmation',
  EDIT_ORDER = 'Edit Order',
  EDIT_ITEMS = 'Edit Items',
  CANCEL_ORDER = 'Cancel Order',
  DOWNLOAD_RECEIPT = 'Download Receipt',
  ADD_COUPON = 'Apply a Coupon',
  REMOVE_COUPON = 'Remove Coupon',
  RESEND_COUPON = 'Apply New Coupon',
  PROCEED_WITHOUT_COUPON = 'Proceed Without Code',
  ADD_ORDER_NOTES = 'Add a Note',
  CHANGE_ORDER_NOTES = 'Change Order Notes',
  GIVE_FEEDBACK = 'Give feedback',
}

export enum MESSAGES {
  WELCOME_MESSAGE,
  WELCOME_CTA_MESSAGE,
  SAME_STORE_INITIATION_MESSAGE,
  DIFFERENT_STORE_INITIATION_MESSAGE,
  CHAT_INITIATION_EXISTING_CUSTOMER,
  NEW_CUSTOMER_NO_STORE_MESSAGE, //reviewed
  EMPTY_SEARCH_RESULTS_MESSAGE,
  SEARCH_RESULTS_NO_EXTRA_MESSAGE,
  STORE_NOT_FOUND_MESSAGE,
  SEARCH_STORE_MESSAGE, //review
  SEARCH_STORE_RESULTS_MESSAGE,
  SEARCH_STORE_RESULTS_EXTRA_MESSAGE,
  UNEXPECTED_RESPONSE_MESSAGE,
  UNSUPPORTED_COUNTRY_MESSAGE,
  CONFIRM_NAME_MESSAGE,
  PICK_ITEMS_MESSAGE,
  EXPIRED_SESSION_MESSAGE,
  PICK_MENU_ITEMS_MESSAGE,
  EMPTY_ITEMS_MATCH_MESSAGE,
  EMPTY_CART_MESSAGE,
  EDIT_ITEMS_MESSAGE,
  ENTER_NAME_MESSAGE,
  CONFIRM_DELIVERY_METHOD_MESSAGE,
  ENTER_DELIVERY_INFO_MESSAGE,
  CANCEL_PROCESS_MESSAGE, //reviewed
  ERROR_MESSAGE,
  PAYMENT_REQUEST_ACCEPTED_MESSAGE,
  PICKUP_ADDRESS_MESSAGE,
  PREVIOUS_DELIVERY_INFO_MESSAGE,
  CONFIRM_PAYMENT_METHOD_MESSAGE,
  PAYMENT_DETAILS_MESSAGE,
  OTHER_PAYMENT_METHOD_MESSAGE,
  SELECT_PREVIOUS_ORDER_MESSAGE,
  PREVIOUS_ORDERS_NOT_FOUND_MESSAGE,
  STORE_CONTACT,
  CONTACT_STORE,
  SUPPORT_CONTACT,
  ORDER_PAYMENT_SUCCESSFUL,
  STORE_UNAVAILABLE,
  STORE_CLOSED,
  ORDER_CONFIRMATION_PENDING_MESSAGE,
  ORDER_CANCELLED,
  EDIT_ORDER,
  ORDER_FULFILLED,
  PAYMENT_RECEIPT,
  PAYMENT_LOADING,
  ORDER_NAVIGATION_RESTRICTED,
  SWITCH_BRANCHES,
  TRANSFER_MADE,
  REQUEST_PROCESSING,
  REQUEST_LOCATION,
  DELIVERY_STATUS,
  HELP_MESSAGE,
  PAYMENT_REMINDER,
  ENTER_COUPON_CODE,
  COUPON_VALIDATION_FAILED,
  COUPON_APPLIED,
  COUPON_REMOVED,
  ORDER_NOTES,
  ORDER_NOTES_ADDED,
  EXPIRED_TEXT,

  //BATCH MESSAGES
  ITEMS_AND_DELIVERY_MESSAGE,
  DELIVERY_DETAILS_MESSAGE,
  ITEMS_OUT_OF_STOCK_MESSAGE,

  // SELLER
  ORDER_EXPIRED_MESSAGE,
  // ORDER_REJECTED_MESSAGE,
  // ORDER_CONFIRMED_MESSAGE,
  ORDER_STATUS_UPDATE_MESSAGE,
  SELLER_ORDER_CONFIRMATION,
  SELF_SESSION_NOT_SUPPORTED,
  PAYMENT_RECEIVED,
  AUTO_DELIVERY_FAILED,
  AUTO_DELIVERY_BOOKED,
  AUTO_DELIVERY_CANCELLED,
  AUTO_DELIVERY_STATUS_UPDATE,
  AUTO_CHECK_IN_MESSAGE,
  FEEDBACK_FORM_MESSAGE,
  FEEDBACK_SUBMITTED_MESSAGE,
  STORE_REMINDER_MESSAGE,
  STORE_OPENED_REMINDER,
}

export const MESSAGE_TEMPLATES: { [key in MESSAGES]: (data?: { [key: string]: any }) => string } = {
  [MESSAGES.WELCOME_MESSAGE]: ({ storeName, deliveryTimeline }) =>
    `Welcome to ${storeName}👋🏽!\n\nI'll be taking your order. ${
      deliveryTimeline ? `It should be delivered in about ${deliveryTimeline}` : ''
    }\n\nPlacing an order takes 2 minutes – what would you like to do?${OTHER_TEXTS.WHITESPACE(2)}`,
  [MESSAGES.WELCOME_CTA_MESSAGE]: ({ storeName, deliveryTimeline }) =>
    `Welcome ${storeName}👋🏽!\n\nI'll be taking your order. ${
      deliveryTimeline ? `It should be delivered in about ${deliveryTimeline}` : ''
    }\n\nPlacing an order takes 2 minutes – Click select items to view the menu, and select items you’d like to buy.${OTHER_TEXTS.WHITESPACE(
      2,
    )}`,
  [MESSAGES.SAME_STORE_INITIATION_MESSAGE]: ({ storeName }) =>
    `Looks like you're currently chatting with ${storeName}! Would you like to continue, or restart the conversation?`,
  [MESSAGES.ITEMS_OUT_OF_STOCK_MESSAGE]: ({}) =>
    `😢 Looks like some of the selected items are currently out of stock. We're sorry for any inconveniences. Please use this link to edit your current selections`,
  [MESSAGES.DIFFERENT_STORE_INITIATION_MESSAGE]: ({ currentStoreName, newStoreName }) =>
    `You currently have an ongoing process with *${currentStoreName}*, would you like to start a new process with *${newStoreName}*?`,
  [MESSAGES.CHAT_INITIATION_EXISTING_CUSTOMER]: ({ storeName }) =>
    `Hi there 👋🏽! It looks like you've been around before.\n\nWould you like to place an order with *${storeName}*, or are you looking to find a different store today?`,
  // [MESSAGES.NEW_CUSTOMER_NO_STORE_MESSAGE]: () =>
  //   `Hey there! Hungry? I can help you order from your favorite restaurants or discover new ones! 🍝\n\nIt only takes 2 minutes to place your order – just tell me the name of the restaurant, and I'll take care of the rest.\n\nJust the name is enough. 😊`,
  [MESSAGES.NEW_CUSTOMER_NO_STORE_MESSAGE]: () =>
    `Hey there! Hungry? What restaurant would you like to order from?\n\nJust the name is enough. 😊`,
  [MESSAGES.EMPTY_SEARCH_RESULTS_MESSAGE]: ({ query }) =>
    `Oops! We couldn't find any restaurants similar to '${query}'.\n\nHow would you like to proceed?`,
  [MESSAGES.SEARCH_RESULTS_NO_EXTRA_MESSAGE]: ({ query }) =>
    `Oops! We couldn't load more restaurants similar to '${query}'.\n\nHow would you like to proceed?`,
  [MESSAGES.STORE_NOT_FOUND_MESSAGE]: () =>
    `Oops! That id didn't match any restaurants.\n\nBut, here's a few other things you can try.`,
  [MESSAGES.EMPTY_ITEMS_MATCH_MESSAGE]: ({ query }) =>
    `🔴 Oops! '${query}' didn't match any items or is currently unavailable.\n\nPlease check the spelling and try again.`,
  [MESSAGES.EMPTY_CART_MESSAGE]: ({}) =>
    `Your cart is currently empty, please send another message with items you'll like you buy.\n${OTHER_TEXTS.WHITESPACE(
      1,
    )}`,
  [MESSAGES.SEARCH_STORE_MESSAGE]: () =>
    `Tell me the name of the restaurant you'll like to order from, and I'll take care of the rest.\n\nJust the name is enough. 😊`,
  [MESSAGES.SEARCH_STORE_RESULTS_MESSAGE]: ({ query, isFirstPage }) =>
    `🔍 Here are ${isFirstPage ? 'a few' : 'more'} restaurants matching '${query}'`,
  [MESSAGES.SEARCH_STORE_RESULTS_EXTRA_MESSAGE]: () =>
    `If you can't find the restaurant you're looking for, you can try any of the following options`,
  [MESSAGES.UNEXPECTED_RESPONSE_MESSAGE]: ({ storeName, showMainOptions, isPaymentStep }) =>
    isPaymentStep
      ? `I’m sorry I didn’t understand your last response.\n\n${formatText(storeName, [
          'capitalize',
          'bold',
        ])} is currently waiting for your payment.\n\nIf you’ve already paid, please wait it’ll be automatically confirmed shortly`
      : `I’m sorry I didn’t understand your last response, would you like to continue your ongoing process or ${
          showMainOptions ? 'go back to the main options' : 'end process'
        }?`,
  [MESSAGES.UNSUPPORTED_COUNTRY_MESSAGE]: ({ name }) => `We are sorry, we don't support your country right now.`,
  [MESSAGES.CONFIRM_NAME_MESSAGE]: ({ name }) =>
    `Can we call you *${getFirstName(
      name,
    )}* or would you like to change your name? Please type your name to reply if you’d like to change your name.`,
  [MESSAGES.PICK_ITEMS_MESSAGE]: ({}) => `Got it, please use this link to select the items you’d like to buy`,
  [MESSAGES.PICK_MENU_ITEMS_MESSAGE]: ({}) =>
    `*Take a look at the menu and reply with what you'll like to buy.*\n\nYou can say I want one plate of...\n${OTHER_TEXTS.WHITESPACE(
      1,
    )}`,
  // To select items, check out the menu and send a message with what you'll like to buy
  [MESSAGES.EDIT_ITEMS_MESSAGE]: ({}) => `Alright, please use this link to edit your current selections`,
  [MESSAGES.ENTER_NAME_MESSAGE]: () => `Send us your full name`,
  [MESSAGES.CONFIRM_DELIVERY_METHOD_MESSAGE]: () =>
    `If you're fine with your selections, please tell us how you'll like to receive this order.`,
  [MESSAGES.ENTER_DELIVERY_INFO_MESSAGE]: ({ isEdit, hasPickupAndDelivery, isAlt }) =>
    isEdit || hasPickupAndDelivery
      ? `Alright got it, please tap the button below to ${isEdit ? 'edit' : 'add'} your delivery information.`
      : `If you're fine with your selections, please click on the button to add your delivery information.`,
  [MESSAGES.CANCEL_PROCESS_MESSAGE]: ({ name, storeName }) =>
    `Thank you for dropping by 😊! You can always come back for delicious food${
      storeName && ` from ${storeName}`
    } whenever you're hungry.`,

  [MESSAGES.ERROR_MESSAGE]: () => `Oops! Something went wrong. Please try again. If this persists contact support`,
  [MESSAGES.STORE_CONTACT]: ({ name }) => `Here's the contact information for *${name}*`,
  [MESSAGES.SUPPORT_CONTACT]: () => `Here's our support contact information`,
  [MESSAGES.CONTACT_STORE]: ({ storeName }) =>
    `💬 Click the button below to chat with ${formatText(storeName, ['capitalize', 'bold'])}`,
  [MESSAGES.ORDER_CONFIRMATION_PENDING_MESSAGE]: ({ storeName }) =>
    `🔔 Please wait while ${storeName} confirms your order. You'll receive payment details when the order is confirmed.${conditionallyAppendWhiteSpace(
      true,
    )}`,
  [MESSAGES.ITEMS_AND_DELIVERY_MESSAGE]: ({
    items,
    totalPrice,
    isAutoSelection,
    invalidItemsString,
    hasInvalidItems = false,
    noOfValidItems,
  }) =>
    `${
      hasInvalidItems
        ? `🔴 Some of the items you selected are currently unavailable:${
            invalidItemsString ? `\n\n${invalidItemsString}` : ''
          }\n-------------------\n\n`
        : ''
    }${
      noOfValidItems > 0
        ? `${
            hasInvalidItems ? 'Here are the items in your cart' : 'Here are your selections'
          }:\n\n${items}\n\nTotal: *${totalPrice}*\n`
        : '_Your cart is currently empty_'
    }\n\n🔔 To edit your selections reply with *add* or *remove* any item, you can also click on edit selections.${conditionallyAppendWhiteSpace(
      isAutoSelection,
    )}`,
  [MESSAGES.PREVIOUS_DELIVERY_INFO_MESSAGE]: ({}) =>
    `Got it, would you like to use your last delivery information or a different one?`,
  [MESSAGES.DELIVERY_DETAILS_MESSAGE]: ({
    name,
    phone,
    address,
    email,
    delivery_fee,
    delivery_area,
    note,
    total_items_cost,
    total_items_count,
  }) =>
    `Here’s the delivery information you provided, kindly crosscheck so your food doesn’t go missing. \n\n🙍🏽‍♂️ Name: ${name}\n📞 Phone: ${phone} ${
      email ? `\n✉️ Email: ${email}` : ''
    } \n\n🌍 *Address Information*\n\nDelivery Area: *${delivery_area.trim()}*\nStreet Address: *${address.trim()}* ${
      note ? `\n\n📝 Order Note: ${note}` : ''
    } \n\n🥡 Total Items (${total_items_count}): ${total_items_cost}\n🚚 Delivery Fee: ${delivery_fee} `,
  [MESSAGES.PICKUP_ADDRESS_MESSAGE]: ({ pickupOnly }) =>
    `${pickupOnly ? `To get your food, you'll need to make a pickup\n\n` : ''}🗺️ Here's the address for your pickup.`,
  [MESSAGES.CONFIRM_PAYMENT_METHOD_MESSAGE]: () =>
    `Your Order has been recorded! One last thing, how would you like to pay for your order?`,
  [MESSAGES.PAYMENT_REQUEST_ACCEPTED_MESSAGE]: () =>
    `Your request has been approved! One last thing, how would you like to pay for your order?`,
  [MESSAGES.PAYMENT_DETAILS_MESSAGE]: ({
    totalItemPrice,
    totalQuantity,
    deliveryFee,
    discountAmount,
    processingFee,
    totalPrice,
    accountName,
    accountNumber,
    hasBankPayment,
    couponCode,
  }) => `Here’s a breakdown of your payment.\n\n🥡 Items Bought (${totalQuantity}): ${totalItemPrice}
    ${deliveryFee ? `\n🚚 Delivery Fee: ${deliveryFee}` : ''}\n${
    hasBankPayment && processingFee ? `⚙️ Payment Fees: ${processingFee}` : ''
  }${
    discountAmount ? `\n🏷️ Discount${couponCode ? `(${couponCode})` : ''}: ${discountAmount}` : ''
  } \n\n*Total: ${totalPrice}*${
    hasBankPayment
      ? `\n\n⚠️ _Please transfer the exact amount, anything less or more will be reversed_\n\n---------------------\n\n🏦 *Pay Into:*\n\n${accountNumber}\n${accountName}`
      : ''
  } ${OTHER_TEXTS.WHITESPACE(2)}`,
  [MESSAGES.OTHER_PAYMENT_METHOD_MESSAGE]: ({ name }) =>
    `Got it *${getFirstName(name)}*, please use this link to pay for your order`,
  [MESSAGES.ORDER_PAYMENT_SUCCESSFUL]: ({ customMessage, deliveryTimeline }) =>
    `✅ Your payment was successful and order has been placed! It should be delivered in about ${deliveryTimeline}\n\n${
      customMessage
        ? `------------------------\n\n${customMessage.trim()}\n\n`
        : "We'll be in touch shortly to update you on the status of your order\n\n"
    }`,
  [MESSAGES.SELECT_PREVIOUS_ORDER_MESSAGE]: () =>
    `*Here are your recent orders*\n\nPlease select an order you'll like to continue with.`,
  [MESSAGES.PREVIOUS_ORDERS_NOT_FOUND_MESSAGE]: () =>
    `Oops! We couldn't find any of your previous orders. What would you like to do?`,
  [MESSAGES.STORE_UNAVAILABLE]: ({ storeName }) =>
    `🔴 Oops! ${storeName} is currently unavailable. But here are a few other things you can try.`,
  [MESSAGES.STORE_CLOSED]: ({ storeName, isInMaintenance, openingTime }) =>
    isInMaintenance
      ? `Hi there, Welcome 👋🏽!\n\n*${capitalizeFirstLetter(
          storeName,
        )}* isn't currently taking orders, but here's a few other things you can try.`
      : `Hi there, Welcome 👋🏽!\n\n*${capitalizeFirstLetter(
          storeName,
        )}* is currently closed at the moment, but you can check back at ${openingTime}. We'll also send you a reminder when they're open.\n\nIn the meantime, here are a few other things you can try.`,
  [MESSAGES.EXPIRED_SESSION_MESSAGE]: ({ storeCode, storeName, hasOrder }) =>
    `⌛ Your ${
      hasOrder ? 'Order was cancelled' : 'session has expired'
    } due to inactivity.\n\nTo place a new order, please click on the link below`,

  [MESSAGES.ORDER_CANCELLED]: ({ reason }) =>
    `🔴 ${formatText('Your order has been cancelled', ['bold'])} ${
      reason ? `\n\n*Reason*: ${reason}` : ''
    }\n\nHow would you like to proceed?`,
  [MESSAGES.PAYMENT_RECEIPT]: ({ storeName }) =>
    `Your payment has been received by *${storeName}*. Here's your receipt`,
  [MESSAGES.ORDER_FULFILLED]: ({ storeName }) =>
    `Your order has been fulfilled. You can always come back for delicious food${
      storeName && ` from ${storeName}`
    } whenever you're hungry. `,

  [MESSAGES.REQUEST_LOCATION]: (data: { isRedo }) =>
    `${
      data.isRedo
        ? `🚫 We couldn't verify your address, please select an address nearby.`
        : `🗺️ Please select the location you'd like this order to be delivered to.`
    }`,
  [MESSAGES.HELP_MESSAGE]: ({}) =>
    `*Here’s a guide to some useful commands:*

      • ${formatText(`Send *back* - _to navigate to previous step_`, [])}
      • ${formatText(`Send *switch* - _to switch between branches_`, [])}
      • ${formatText(`Send *menu* - _to view the menu & pick items_`, [])}
      • ${formatText(`Send *search* - _to search for another store_`, [])}
      • ${formatText(`Send *refresh* - _to go back to the main options_`, [])}
      • ${formatText(`Send *end* - _to end your current chat_`, [])} ${OTHER_TEXTS.WHITESPACE(2)}
  `,

  [MESSAGES.PAYMENT_REMINDER]: ({ storeName }) =>
    `⏳ Hi there 👋🏽, ${formatText(storeName, [
      'bold',
      'capitalize',
    ])} is still waiting for your payment.\n\nIf you’ve already paid it’ll be automatically confirmed shortly, if you’ll like to pay with other methods click on the button below`,

  [MESSAGES.STORE_OPENED_REMINDER]: ({ storeName }) =>
    `We're excited to let you know that ${formatText(storeName, [
      'bold',
      'capitalize',
    ])} is open and ready to serve you today! 🍽️`,
  //SELLER MESSAGES START

  [MESSAGES.ORDER_EXPIRED_MESSAGE]: ({ items }) =>
    `This order has been automatically cancelled due to inactivity\n\nOrder Items:\n\n${items}`,
  [MESSAGES.PAYMENT_RECEIVED]: ({
    items,
    isDelivery,
    totalAmount,
    orderId,
    deliveryInfo: { name, phone, address, delivery_area } = {} as any,
    note,
  }) =>
    `*💰 Payment Received*\n\nYou have received *${totalAmount}* for order ${orderId}\n\n🛵 Order Type: ${
      isDelivery ? 'Delivery' : 'Pickup'
    } \n\nOrder Items:\n\n${items}\n\n🙍🏽‍♂️Customer Information:\n\nName: ${name}\nPhone: ${phone}${
      isDelivery ? `\n\n🌍 Delivery Information\n\nDelivery Area: ${delivery_area}\nStreet Address: ${address}` : ''
    }${note ? `\n\n📝 Order Note: ${note}` : ''}`,
  [MESSAGES.ORDER_STATUS_UPDATE_MESSAGE]: ({ status, orderId, previouslyUpdated, reason = '' }) =>
    status === ORDER_STATUSES.CANCELLED
      ? `🔴 *Order Cancelled*\n\nOrder ${orderId} ${
          previouslyUpdated ? 'was previously' : 'has been'
        } cancelled, and the customer ${previouslyUpdated ? 'was' : 'has been'} notified with reason: _${reason}_`
      : `✅ *Order Confirmed*\n\nOrder ${orderId} has been confirmed successfully, and a payment request has been sent to the customer.`,
  [MESSAGES.AUTO_DELIVERY_CANCELLED]: () => null,
  [MESSAGES.AUTO_DELIVERY_FAILED]: () => null,
  [MESSAGES.AUTO_DELIVERY_BOOKED]: () => null,
  [MESSAGES.AUTO_CHECK_IN_MESSAGE]: () => null,
  [MESSAGES.FEEDBACK_FORM_MESSAGE]: () =>
    `⭐ We're happy to hear back from you, please give any feedback you have about your last order.`,
  [MESSAGES.FEEDBACK_SUBMITTED_MESSAGE]: () => `Thank you for your feedback! Would you like to place a new order?`,
  [MESSAGES.SELLER_ORDER_CONFIRMATION]: ({ body: { items, totalCartPrice, deliveryAddress }, buttons }) => `
  ⚠️ *Please provide a reason for rejecting this order*\n\nOrder Items:\n\n${items}\n\nTotal: ${totalCartPrice}\n\n${
    deliveryAddress ? `*Delivery Address*\n\n${deliveryAddress}\n\n` : ''
  }---------------------------\nYou can also view and confirm this order here: ${process.env.CATLOG_WWW}/o/${
    buttons.orderId
  }\n---------------------------\n🔴 Please note that if this order isn't confirmed in 5 minutes, it'll be auto-cancelled
  `,
  [MESSAGES.SELF_SESSION_NOT_SUPPORTED]: ({}) =>
    `🚫 *You cannot place an order as the business owner, to test chowbot, try texting with a different phone number*`,
  [MESSAGES.AUTO_DELIVERY_STATUS_UPDATE]: () => null,

  //SELLER MESSAGES END
  [MESSAGES.ORDER_NOTES]: ({}) => `📝 Please provide any additional notes you’d like to add to this order`,
  [MESSAGES.ORDER_NOTES_ADDED]: ({ orderNotes }) =>
    `✅ Order notes successfully added!\n\nNote: ${formatText(orderNotes, ['bold']) || 'No notes added'}`,
  [MESSAGES.ENTER_COUPON_CODE]: ({}) => `🎟️ Understood! Please respond with the coupon you'd like to add to this order`,
  [MESSAGES.COUPON_VALIDATION_FAILED]: ({ message }) =>
    `🔴 The coupon code you entered could not be applied to this order.\n\n*Reason:* ${
      message ?? 'This coupon is invalid or has been used'
    }\n\nPlease provide a new coupon code or proceed without a coupon`,
  [MESSAGES.COUPON_APPLIED]: ({ couponCode, couponAmount }) =>
    `✅ Your coupon *${couponCode.toUpperCase()}* was applied, you now have a discount of *${couponAmount}*.`,
  [MESSAGES.COUPON_REMOVED]: ({}) => `✅ Coupon successfully removed!`,

  [MESSAGES.PAYMENT_LOADING]: ({}) => `✅ Order Confirmed\n\nGenerating payment details...`,
  [MESSAGES.REQUEST_PROCESSING]: ({}) => `Processing your request...`,
  [MESSAGES.ORDER_NAVIGATION_RESTRICTED]: ({}) =>
    `🚫 *Your order has been processed. You cannot make changes at this time*`,
  [MESSAGES.SWITCH_BRANCHES]: ({ storeName }) =>
    `🏁 ${storeName} has other branches.\n\nIf you'd like to order from a different branch, click here to switch branches`,
  [MESSAGES.EDIT_ORDER]: ({}) =>
    `Would you like to make changes to the items you selected or your delivery information?`,
  [MESSAGES.TRANSFER_MADE]: ({}) => `Please wait while we confirm your transfer...`,
  [MESSAGES.EXPIRED_TEXT]: ({}) => `Sorry this message has expired. Send a new message to initiate a new session`,
  [MESSAGES.STORE_REMINDER_MESSAGE]: ({ count = 0 }) =>
    count === 0
      ? `⏰ It looks like you’ve been inactive for a while, your session is about to expire.\n\nWould you like to continue your order?`
      : `⏰ We didn't get any response to the last message, would you like to continue your order?`,
  [MESSAGES.DELIVERY_STATUS]: ({
    status,
    items,
    storeName,
    courier,
  }: {
    status: DELIVERY_STATUS;
    items: string;
    storeName: string;
    courier: string;
  }) => {
    let messageBody = '';
    switch (status) {
      case DELIVERY_STATUS.COMPLETED:
        messageBody = `*✅ Order Delivered*\n\nYour order has been delivered, thank you for choosing ${storeName}`;
        break;
      case DELIVERY_STATUS.IN_TRANSIT:
        messageBody = `*🚚 Rider in Transit*\n\nYour order from ${storeName} is on the way to you`;
        break;
      case DELIVERY_STATUS.AT_CUSTOMER_LOCATION:
        messageBody = `*🏠 Rider has arrived*\n\nYour order from ${storeName} has arrived at your location`;
        break;
      case DELIVERY_STATUS.CANCELLED:
        messageBody = `*🔴 Delivery Cancelled*\n\nYour delivery with ${storeName} has been cancelled, please reach out to ${storeName} for next steps`;
        break;
      case DELIVERY_STATUS.CONFIRMED:
        messageBody = `*✅ Delivery Booked*\n\n${storeName} has booked a delivery for your order with ${courier}, please check the tracking link for your confirmation code`;
        break;
      case DELIVERY_STATUS.PICKED_UP:
        messageBody = `*🥡 Order Picked Up*\n\nA rider has picked up your order from ${storeName} and will be on their way to you shortly`;
        break;
    }

    // messageBody += `\n\nOrder Items:\n\n${items}`;
    return messageBody;
  },
};

export const LOG_MESSAGES = {
  [MESSAGES.DELIVERY_DETAILS_MESSAGE]: ({ name, phone, address, email, delivery_area }) =>
    `🙍🏽‍♂️ Name: ${name}\n📞 Phone: ${phone} ${
      email ? `\n✉️ Email: ${email}` : ''
    } \n\n🌍 Address Information\n\nDelivery Area: ${delivery_area}\nStreet Address: ${address}`,
};

export const OTHER_TEXTS = {
  //COMPUTED
  WHITESPACE: (lines: number) => new Array(lines).fill('‎\n').join(''),
  WELCOME_STORE_MESSAGE: ({ storeName }) => `Welcome to *${storeName}*`,
  CONFIRMATION_MESSAGE_TITLE: (isEscalated: boolean) =>
    isEscalated ? 'Awaiting Order Confirmation - No Response Received' : 'You have a new Order',
  CONFIRMATION_EXPIRED: ({ storeName }) => `${storeName} took too long to confirm your order`,

  // FOOTERS
  GO_BACK_FOOTER: `Text 'back' to navigate to previous step`,
  USE_LINK_FOOTER: `You can also select items by clicking the link below`,
  AI_DISCLAIMER_FOOTER: `ⓘ Powered by AI`,
  TOO_LONG_FOOTER: 'Select an option if this is taking too long',
  CUSTOMER_NOTIFIED_FOOTER: 'ⓘ Customer has been notified',
  DELIVERY_FOOTER: 'ⓘ Please provide accurate details',
  LOCATION_REQUIRED_FOOTER: `ⓘ You'll be required to send a location to proceed`,
  AUTO_CONFIRMATION_FOOTER: 'ⓘ Payment would be automatically confirmed',
  TRACK_DELIVERY_FOOTER: 'ⓘ Click the link below to track your delivery',
  HELP_FOOTER: `ⓘ Type / to display useful commands`,
  CONTACT_SUPPORT_FOOTER: `ⓘ Contact support for further assistance`,
};
