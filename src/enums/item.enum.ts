export enum ITEM_VARIANTS {
  IMAGES = 'images',
  CUSTOM = 'custom',
}

export enum ITEM_SOURCE {
  MANUAL = 'MANUAL',
  INSTAGRAM = 'INSTAGRAM',
  CHOWDECK = 'CHOWDECK',
  MENU_IMAGE = 'MENU_IMAGE',
}
// filter tags
export enum PRODUCT_AVAILABILITY_TAG {
  IN_STOCK = 'in stock',
  OUT_OF_STOCK = 'out of stock',
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
}

export enum PRODUCT_VARIANT_TAG {
  HAS_VARIANTS = 'has variants',
  NO_VARIANTS = 'no variants',
  IMAGE_VARIANTS = 'image variants',
  CUSTOM_VARIANTS = 'custom variants',
}

export enum PRODUCT_FEATURED_TAG {
  FEATURED = 'featured',
  NOT_FEATURED = 'not featured',
}

export enum PRODUCT_DISCOUNT_TAG {
  HAS_DISCOUNT = 'has discount',
  NO_DISCOUNT = 'no discount',
}

// Discounts
export enum DISCOUNT_TYPE_TAG {
  HAS_CAP = 'has cap',
  NO_CAP = 'no cap',
}

export enum DISCOUNT_STATUS_TAG {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// Coupons
export enum COUPON_TYPE_TAG {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
  HAS_MINIMUM_PURCHASE = 'has minimum purchase',
  NO_MINIMUM_PURCHASE = 'no minimum purchase',
}

export enum COUPON_STATUS_TAG {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

