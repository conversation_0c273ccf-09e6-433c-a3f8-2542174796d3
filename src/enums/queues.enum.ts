export enum QUEUES {
  ORDER = 'ORDER',
  BOT = 'BOT',
  DELETED_IMAGES_MAIL = 'DELETED_IMAGES_MAIL',
  WALLET = 'WALLET',
  PAYMENT = 'PAYMENT',
  USER = 'USER',
  WHATSAPP_BOT = 'WHATSAPP_BOT',
  STORE = 'STORE',
  ITEM = 'ITEM',
  MAIL = 'MAIL',
  DOMAIN = 'DOMAIN',
  STOCK_THRESHOLD = 'STOCK_THRESHOLD',
}

export enum JOBS {
  TWEET_PRODUCT = 'TWEET_PRODUCT',
  TWEET_EXISTING_PRODUCT = 'TWEET_EXISTING_PRODUCT',
  DELETE_PRODUCT_TWEET = 'DELETE_PRODUCT_TWEET',
  SEND_DELETED_IMAGES_MAIL = 'SEND_DELETED_IMAGES_MAIL',
  IMPORT_ITEMS = 'IMPORT_ITEMS',

  WALLET_SQUAD_PAYMENT = 'WALLET_SQUAD_PAYMENT',
  WALLET_PAYAZA_PAYMENT = 'WALLET_PAYAZA_PAYMENT',
  WALLET_BANK_PAYMENT = 'WALLET_BANK_PAYMENT',
  WALLET_WITHDRAWAL = 'WALLET_WITHDRAW',
  WALLET_CURRENCY_CONVERSION_DEBIT = 'WALLET_CURRENCY_CONVERSION_DEBIT',
  WALLET_CURRENCY_CONVERSION_CREDIT = 'WALLET_CURRENCY_CONVERSION_CREDIT',
  REQUERY_SQUAD_PAYOUT = 'REQUERY_SQUAD_PAYOUT',

  ORDER_VOLUME_MILESTONE = 'ORDER_VOLUME_MILESTONE',
  CHECK_ORDER_PAYMENT = 'CHECK_ORDER_PAYMENT',
  PAYMENT_VOLUME_MILESTONE = 'PAYMENT_VOLUME_MILESTONE',
  // STORE_VISIT_MILESTONE = 'STORE_VISIT_MILESTONE',
  ORDER_COUNT_MILESTONE = 'ORDER_COUNT_MILESTONE',
  SEND_WELCOME_EMAILS = 'SEND_WELCOME_EMAILS',
  PUBLIC_PAYMENT = 'PUBLIC_PAYMENT',
  DELAYED_DELIVERY = 'DELAYED_DELIVERY',

  WABOT_SESSION_TIMEOUT = 'WABOT_SESSION_TIMEOUT',
  WABOT_PAYMENT_REMINDER = 'WABOT_PAYMENT_REMINDER',
  ORDER_CONFIRMATION_TIMEOUT = 'ORDER_CONFIRMATION_TIMEOUT',
  EXPORT_ITEMS_TO_CHOWDECK = 'EXPORT_ITEMS_TO_CHOWDECK',
  IMPORT_CHOWDECK_ITEMS = 'IMPORT_CHOWDECK_ITEMS',
  SYNC_STORE_TO_CHOWDECK = 'SYNC_STORE_TO_CHOWDECK',
  SYNC_CHOWDECK_TO_STORE = 'SYNC_CHOWDECK_TO_STORE',
  SYNC_SINGLE_ITEM_TO_CHOWDECK = 'SYNC_SINGLE_ITEM_TO_CHOWDECK',
  REFRESH_STORE_MENU = 'REFRESH_STORE_MENU',
  SEND_STORE_OPENED_REMINDER = 'SEND_STORE_OPENED_REMINDER',
  SEND_EMAIL_SUMMARY = 'SEND_EMAIL_SUMMARY',
  DOMAIN_DNS_SETUP = 'DOMAIN_DNS_SETUP',
  GENERATE_EMBEDDING = 'GENERATE_EMBEDDING',
  CHECK_STOCK_THRESHOLD = 'CHECK_STOCK_THRESHOLD',
}
